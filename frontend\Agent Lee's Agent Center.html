<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><PERSON>'s Agent Center</title>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=Orbitron:wght@400;500;700;900&display=swap" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/all.min.css" rel="stylesheet">
    <style>
        :root {
            --bg-gradient-1: #0a0e29;
            --bg-gradient-2: #1a1040;
            --bg-gradient-3: #0f0520;
            --primary-color: #00f2ff;
            --primary-glow: rgba(0, 242, 255, 0.6);
            --secondary-color: #9854ff;
            --accent-color: #ff2a6d;
            --accent-glow: rgba(255, 42, 109, 0.6);
            --text-color: #e0e7ff;
            --panel-bg: rgba(16, 20, 40, 0.4);
            --panel-border: rgba(0, 242, 255, 0.15);
            --panel-header: rgba(24, 26, 44, 0.8);
            --chart-blue: #00d1ff;
            --chart-purple: #9854ff;
            --chart-red: #ff416c;
            --chart-yellow: #ffd166;
            --chart-green: #06d6a0;
            --shadow: rgba(0, 0, 0, 0.3);
            --card-glow: 0 0 20px rgba(0, 242, 255, 0.15);
            --header-glow: 0 5px 25px rgba(0, 242, 255, 0.15);
        }

        * {
            box-sizing: border-box;
            margin: 0;
            padding: 0;
        }

        body {
            font-family: 'Inter', sans-serif;
            background: linear-gradient(135deg, var(--bg-gradient-1), var(--bg-gradient-2), var(--bg-gradient-3));
            background-size: 400% 400%;
            animation: gradientBG 15s ease infinite;
            color: var(--text-color);
            min-height: 100vh;
            overflow-x: hidden;
            display: flex;
            flex-direction: column;
            position: relative;
        }

        @keyframes gradientBG {
            0% { background-position: 0% 50% }
            50% { background-position: 100% 50% }
            100% { background-position: 0% 50% }
        }

        /* Background Effects */
        .background-effect {
            position: absolute;
            width: 100%;
            height: 100%;
            top: 0;
            left: 0;
            pointer-events: none;
            z-index: 0;
            overflow: hidden;
        }

        .holographic-grid {
            position: absolute;
            width: 200%;
            height: 200%;
            top: -50%;
            left: -50%;
            background-image: 
                linear-gradient(rgba(0, 242, 255, 0.05) 1px, transparent 1px),
                linear-gradient(90deg, rgba(0, 242, 255, 0.05) 1px, transparent 1px);
            background-size: 40px 40px;
            transform: perspective(500px) rotateX(60deg);
            animation: gridMove 20s linear infinite;
            opacity: 0.3;
        }

        @keyframes gridMove {
            0% { background-position: 0 0; }
            100% { background-position: 0 40px; }
        }

        .floating-particles {
            position: absolute;
            width: 100%;
            height: 100%;
            top: 0;
            left: 0;
        }

        .particle {
            position: absolute;
            width: 2px;
            height: 2px;
            background: var(--primary-color);
            border-radius: 50%;
            box-shadow: 0 0 10px var(--primary-glow);
            animation: float 20s infinite linear;
        }

        @keyframes float {
            0% {
                transform: translateY(0) translateX(0);
                opacity: 0;
            }
            10% { opacity: 1; }
            90% { opacity: 1; }
            100% {
                transform: translateY(-100vh) translateX(100px);
                opacity: 0;
            }
        }

        /* Header Section */
        .header {
            position: relative;
            z-index: 10;
            padding: 0;
            border-bottom: 1px solid var(--panel-border);
            background: linear-gradient(to right, rgba(10, 14, 41, 0.8), rgba(30, 15, 70, 0.8));
            box-shadow: var(--header-glow);
            backdrop-filter: blur(10px);
            clip-path: polygon(0 0, 100% 0, 100% 85%, 97% 100%, 0 100%);
            margin-bottom: 20px;
        }

        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 22px 35px;
        }

        .logo-container {
            position: relative;
            display: flex;
            align-items: center;
        }

        .logo {
            font-family: 'Orbitron', sans-serif;
            font-weight: 700;
            font-size: 28px;
            color: var(--primary-color);
            text-shadow: 0 0 15px rgba(0, 242, 255, 0.7);
            position: relative;
            margin-left: 15px;
        }

        .logo-icon {
            position: relative;
            width: 45px;
            height: 45px;
            margin-right: 15px;
        }

        .logo-icon .circle-outer {
            position: absolute;
            width: 100%;
            height: 100%;
            border: 2px solid var(--primary-color);
            border-radius: 50%;
            animation: pulse 2s infinite;
        }

        .logo-icon .circle-inner {
            position: absolute;
            width: 60%;
            height: 60%;
            top: 20%;
            left: 20%;
            border: 1.5px solid var(--secondary-color);
            border-radius: 50%;
            animation: pulse 2s infinite 0.3s;
        }

        .logo-icon .circle-core {
            position: absolute;
            width: 30%;
            height: 30%;
            top: 35%;
            left: 35%;
            background: var(--accent-color);
            border-radius: 50%;
            box-shadow: 0 0 10px var(--accent-glow);
            animation: glow 2s infinite alternate;
        }

        .neon-text {
            color: var(--accent-color);
            text-shadow: 
                0 0 5px rgba(255, 42, 109, 0.5),
                0 0 10px rgba(255, 42, 109, 0.5),
                0 0 15px rgba(255, 42, 109, 0.5);
            font-size: 0.8em;
            vertical-align: middle;
        }

        .status-module {
            display: flex;
            align-items: center;
            background: rgba(16, 20, 40, 0.6);
            padding: 8px 20px;
            border-radius: 30px;
            border: 1px solid var(--panel-border);
            box-shadow: 0 0 15px rgba(0, 242, 255, 0.2);
        }

        .status-indicator {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 12px;
            background: #34a853;
            box-shadow: 0 0 10px rgba(52, 168, 83, 0.7);
            animation: pulse 2s infinite;
        }

        /* Main Container */
        .main-container {
            flex: 1;
            display: flex;
            flex-direction: column;
            gap: 25px;
            padding: 0 30px 30px;
            position: relative;
            z-index: 1;
            max-width: 1800px;
            margin: 0 auto;
            width: 100%;
        }

        /* Control Bar */
        .control-bar {
            background: var(--panel-bg);
            backdrop-filter: blur(10px);
            border-radius: 15px;
            border: 1px solid var(--panel-border);
            padding: 15px 20px;
            display: flex;
            flex-wrap: wrap;
            gap: 12px;
            justify-content: center;
            align-items: center;
            box-shadow: var(--card-glow);
            position: relative;
            overflow: hidden;
        }

        .control-bar::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(0, 242, 255, 0.1), transparent);
            transform: translateX(-100%);
            animation: scanline 8s linear infinite;
            pointer-events: none;
        }

        @keyframes scanline {
            0% { transform: translateX(-100%); }
            50%, 100% { transform: translateX(100%); }
        }

        .control-button {
            background: rgba(14, 22, 45, 0.7);
            border: 1px solid var(--panel-border);
            color: var(--text-color);
            padding: 12px 18px;
            border-radius: 8px;
            font-family: 'Inter', sans-serif;
            font-size: 14px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            display: flex;
            align-items: center;
            gap: 8px;
            position: relative;
            overflow: hidden;
        }

        .control-button.primary {
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            border: none;
            color: #fff;
            font-weight: 600;
        }

        .control-button:hover {
            transform: translateY(-3px);
            box-shadow: 0 7px 14px rgba(0, 0, 0, 0.15), 0 0 10px rgba(0, 242, 255, 0.3);
        }

        /* Main Content Area */
        .content-area {
            display: grid;
            grid-template-columns: 1fr 300px;
            gap: 25px;
            height: calc(100vh - 300px);
        }

        /* Agent Display Container */
        .agent-display-container {
            background: var(--panel-bg);
            backdrop-filter: blur(10px);
            border-radius: 15px;
            border: 1px solid var(--panel-border);
            overflow: hidden;
            box-shadow: var(--card-glow);
            position: relative;
            display: flex;
            flex-direction: column;
        }

        .display-header {
            background: var(--panel-header);
            padding: 18px 20px;
            border-bottom: 1px solid var(--panel-border);
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .display-title {
            font-family: 'Orbitron', sans-serif;
            font-size: 18px;
            font-weight: 600;
            color: var(--text-color);
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .display-title i {
            color: var(--primary-color);
        }

        .agent-count {
            background: rgba(0, 0, 0, 0.2);
            color: var(--primary-color);
            padding: 6px 12px;
            border-radius: 20px;
            font-size: 14px;
            font-family: 'Orbitron', sans-serif;
            font-weight: 500;
            border: 1px solid var(--panel-border);
        }

        /* Holographic Agent Display */
        .holographic-display {
            flex: 1;
            position: relative;
            background: linear-gradient(45deg, rgba(0, 0, 0, 0.2), rgba(0, 242, 255, 0.02));
            display: flex;
            justify-content: center;
            align-items: center;
            overflow: hidden;
        }

        .holographic-platform {
            position: absolute;
            width: 300px;
            height: 10px;
            background: linear-gradient(90deg, transparent, var(--primary-color), transparent);
            border-radius: 50%;
            bottom: 120px;
            filter: blur(5px);
            opacity: 0.7;
            box-shadow: 0 0 20px var(--primary-glow);
        }

        .agent-hologram {
            position: relative;
            width: 200px;
            height: 300px;
            display: flex;
            flex-direction: column;
            align-items: center;
            transform-style: preserve-3d;
            transform: translateY(-30px);
            animation: float-hologram 4s ease-in-out infinite;
        }

        @keyframes float-hologram {
            0%, 100% { transform: translateY(-30px); }
            50% { transform: translateY(-40px); }
        }

        .agent-silhouette {
            width: 100px;
            height: 180px;
            background: linear-gradient(to bottom, var(--primary-color), var(--secondary-color));
            clip-path: polygon(
                30% 0%, 70% 0%, 
                70% 10%, 80% 10%, 
                80% 20%, 70% 20%,
                70% 40%, 80% 45%,
                80% 50%, 70% 55%,
                70% 80%, 60% 100%,
                40% 100%, 30% 80%,
                30% 55%, 20% 50%,
                20% 45%, 30% 40%,
                30% 20%, 20% 20%,
                20% 10%, 30% 10%
            );
            opacity: 0.7;
            position: relative;
            margin-bottom: 20px;
        }

        .agent-silhouette::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: linear-gradient(transparent, var(--primary-color));
            opacity: 0.3;
            animation: pulse-silhouette 2s ease-in-out infinite alternate;
        }

        @keyframes pulse-silhouette {
            0% { opacity: 0.2; }
            100% { opacity: 0.5; }
        }

        .agent-scan-line {
            position: absolute;
            width: 100%;
            height: 5px;
            background: linear-gradient(90deg, transparent, var(--primary-color), transparent);
            animation: scan-agent 3s ease-in-out infinite;
            opacity: 0.7;
            filter: blur(2px);
        }

        @keyframes scan-agent {
            0%, 100% { top: 0; }
            50% { top: 180px; }
        }

        .agent-name {
            font-family: 'Orbitron', sans-serif;
            font-size: 20px;
            font-weight: 700;
            color: var(--primary-color);
            text-shadow: 0 0 10px var(--primary-glow);
            margin-bottom: 5px;
            text-align: center;
        }

        .agent-id {
            font-family: 'Inter', sans-serif;
            font-size: 14px;
            color: var(--text-color);
            opacity: 0.8;
            margin-bottom: 15px;
        }

        .agent-function {
            font-family: 'Inter', sans-serif;
            font-size: 12px;
            color: var(--accent-color);
            background: rgba(0, 0, 0, 0.3);
            padding: 4px 10px;
            border-radius: 10px;
            border: 1px solid var(--accent-color);
        }

        /* Agent Carousel */
        .agent-list-container {
            background: rgba(0, 0, 0, 0.3);
            padding: 20px;
            border-top: 1px solid var(--panel-border);
            overflow: hidden;
            position: relative;
        }

        .agent-list {
            display: flex;
            gap: 15px;
            overflow-x: auto;
            padding: 10px 0;
            scroll-behavior: smooth;
        }

        .agent-list::-webkit-scrollbar {
            height: 8px;
        }

        .agent-list::-webkit-scrollbar-track {
            background: rgba(0, 0, 0, 0.2);
            border-radius: 4px;
        }

        .agent-list::-webkit-scrollbar-thumb {
            background: var(--primary-color);
            border-radius: 4px;
        }

        .agent-card {
            min-width: 120px;
            height: 160px;
            background: rgba(10, 14, 30, 0.5);
            border: 1px solid var(--panel-border);
            border-radius: 8px;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            padding: 10px;
            cursor: pointer;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .agent-card.active {
            border-color: var(--primary-color);
            box-shadow: 0 0 15px var(--primary-glow);
        }

        .agent-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 20px rgba(0, 0, 0, 0.2);
        }

        .agent-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: linear-gradient(45deg, transparent, rgba(0, 242, 255, 0.1), transparent);
            transform: translateX(-100%);
            animation: card-sweep 4s infinite;
            pointer-events: none;
        }

        @keyframes card-sweep {
            0% { transform: translateX(-100%); }
            20%, 100% { transform: translateX(100%); }
        }

        .card-avatar {
            width: 50px;
            height: 50px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 24px;
            background: linear-gradient(135deg, var(--bg-gradient-1), var(--bg-gradient-2));
            border-radius: 50%;
            margin-bottom: 10px;
            position: relative;
        }

        .card-avatar::after {
            content: '';
            position: absolute;
            width: 54px;
            height: 54px;
            border: 1px solid var(--primary-color);
            border-radius: 50%;
            opacity: 0.6;
        }

        .card-name {
            font-size: 14px;
            font-weight: 600;
            color: var(--text-color);
            text-align: center;
            margin-bottom: 4px;
        }

        .card-id {
            font-size: 10px;
            color: var(--text-color);
            opacity: 0.7;
        }

        .list-controls {
            position: absolute;
            left: 0;
            width: 100%;
            display: flex;
            justify-content: space-between;
            padding: 0 15px;
            pointer-events: none;
            top: 50%;
            transform: translateY(-50%);
        }

        .list-control {
            width: 40px;
            height: 40px;
            background: rgba(0, 0, 0, 0.5);
            border: 1px solid var(--panel-border);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            pointer-events: auto;
            transition: all 0.3s ease;
            color: var(--primary-color);
        }

        .list-control:hover {
            background: rgba(0, 242, 255, 0.2);
            transform: scale(1.1);
        }

        /* Right Sidebar */
        .sidebar {
            display: flex;
            flex-direction: column;
            gap: 20px;
        }

        /* Metrics Panel */
        .metrics-panel {
            background: var(--panel-bg);
            backdrop-filter: blur(10px);
            border-radius: 15px;
            border: 1px solid var(--panel-border);
            overflow: hidden;
            box-shadow: var(--card-glow);
        }

        .metrics-header {
            background: var(--panel-header);
            padding: 18px 20px;
            border-bottom: 1px solid var(--panel-border);
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .metrics-title {
            font-family: 'Orbitron', sans-serif;
            font-size: 16px;
            font-weight: 600;
            color: var(--text-color);
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .metrics-title i {
            color: var(--primary-color);
        }

        .metrics-content {
            padding: 20px;
        }

        .metric-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
            padding: 10px;
            background: rgba(0, 0, 0, 0.2);
            border-radius: 8px;
            border: 1px solid rgba(255, 255, 255, 0.05);
        }

        .metric-label {
            font-size: 12px;
            color: rgba(224, 231, 255, 0.7);
        }

        .metric-value {
            font-family: 'Orbitron', sans-serif;
            font-size: 18px;
            font-weight: 700;
            color: var(--primary-color);
            text-shadow: 0 0 10px rgba(0, 242, 255, 0.5);
        }

        /* Agent Info Panel */
        .agent-info-panel {
            background: var(--panel-bg);
            backdrop-filter: blur(10px);
            border-radius: 15px;
            border: 1px solid var(--panel-border);
            overflow: hidden;
            box-shadow: var(--card-glow);
            flex: 1;
        }

        .agent-info-content {
            padding: 20px;
            height: 100%;
            overflow-y: auto;
            display: flex;
            flex-direction: column;
            gap: 15px;
        }

        .info-category {
            font-family: 'Inter', sans-serif;
            font-size: 12px;
            color: var(--accent-color);
            background: rgba(0, 0, 0, 0.3);
            padding: 4px 10px;
            border-radius: 10px;
            border: 1px solid var(--accent-color);
            display: inline-block;
            margin-bottom: 10px;
        }

        .info-section {
            margin-bottom: 20px;
        }

        .info-section h4 {
            color: var(--primary-color);
            margin-bottom: 10px;
            font-size: 14px;
        }

        .tool-tag {
            background: rgba(0, 242, 255, 0.1);
            border: 1px solid var(--primary-color);
            color: var(--primary-color);
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 11px;
            margin: 2px;
            display: inline-block;
        }

        .info-description {
            font-size: 14px;
            line-height: 1.6;
            color: rgba(224, 231, 255, 0.8);
            margin-bottom: 15px;
        }

        .performance-section {
            margin-bottom: 15px;
        }

        .performance-bar {
            background: rgba(0, 0, 0, 0.3);
            height: 6px;
            border-radius: 3px;
            overflow: hidden;
            margin-top: 5px;
        }

        .performance-fill {
            height: 100%;
            background: linear-gradient(90deg, var(--primary-color), var(--secondary-color));
            border-radius: 3px;
            transition: width 0.5s ease;
        }

        /* Loading Animation */
        .loading-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: linear-gradient(135deg, var(--bg-gradient-1), var(--bg-gradient-2), var(--bg-gradient-3));
            display: flex;
            justify-content: center;
            align-items: center;
            z-index: 1000;
            opacity: 1;
            transition: opacity 0.8s ease;
        }

        .loading-overlay.hidden {
            opacity: 0;
            pointer-events: none;
        }

        .loading-content {
            text-align: center;
        }

        .loading-spinner {
            width: 100px;
            height: 100px;
            border: 3px solid transparent;
            border-top-color: var(--primary-color);
            border-right-color: var(--secondary-color);
            border-bottom-color: var(--accent-color);
            border-radius: 50%;
            animation: spin 2s linear infinite;
            margin: 0 auto 20px;
        }

        .loading-text {
            font-family: 'Orbitron', sans-serif;
            color: var(--primary-color);
            font-size: 24px;
            font-weight: 700;
            text-shadow: 0 0 15px rgba(0, 242, 255, 0.7);
        }

        /* Animations */
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        @keyframes pulse {
            0%, 100% {
                opacity: 1;
                transform: scale(1);
            }
            50% {
                opacity: 0.8;
                transform: scale(0.95);
            }
        }

        @keyframes glow {
            0% {
                box-shadow: 0 0 5px var(--accent-glow);
            }
            100% {
                box-shadow: 0 0 20px var(--accent-glow), 0 0 30px var(--accent-glow);
            }
        }

        /* Responsive Design */
        @media (max-width: 1200px) {
            .content-area {
                grid-template-columns: 1fr;
                gap: 15px;
            }

            .sidebar {
                display: grid;
                grid-template-columns: 1fr 1fr;
                gap: 15px;
            }
        }

        @media (max-width: 768px) {
            .header-content {
                flex-direction: column;
                gap: 15px;
                text-align: center;
                padding: 15px;
            }

            .control-bar {
                justify-content: center;
                flex-wrap: wrap;
            }

            .content-area {
                height: auto;
            }

            .sidebar {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <!-- Loading Screen -->
    <div class="loading-overlay" id="loadingOverlay">
        <div class="loading-content">
            <div class="loading-spinner"></div>
            <div class="loading-text">Initializing Agent Lee's Agents</div>
        </div>
    </div>

    <!-- Background Effects -->
    <div class="background-effect">
        <div class="holographic-grid"></div>
        <div class="floating-particles" id="particles"></div>
    </div>

    <!-- Header -->
    <header class="header">
        <div class="header-content">
            <div class="logo-container">
                <div class="logo-icon">
                    <div class="circle-outer"></div>
                    <div class="circle-inner"></div>
                    <div class="circle-core"></div>
                </div>
                <div class="logo">Agent Lee's <span class="neon-text">Agent Center</span></div>
            </div>
            <div class="status-module">
                <span class="status-indicator"></span>
                <span>System Active</span>
            </div>
        </div>
    </header>

    <!-- Main Container -->
    <div class="main-container">
        <!-- Control Bar -->
        <div class="control-bar">
            <button class="control-button primary" id="addAgentBtn">
                <i class="fas fa-plus-circle"></i> Add Agent
            </button>
            <button class="control-button" id="removeAgentBtn">
                <i class="fas fa-minus-circle"></i> Remove Agent
            </button>
            <button class="control-button" id="assignTaskBtn">
                <i class="fas fa-tasks"></i> Assign Task
            </button>
            <button class="control-button" id="linkAgentsBtn">
                <i class="fas fa-link"></i> Link Agents
            </button>
        </div>

        <!-- Main Content Area -->
        <div class="content-area">
            <!-- Agent Display Container -->
            <div class="agent-display-container">
                <div class="display-header">
                    <div class="display-title">
                        <i class="fas fa-robot"></i> Agent Hologram Display
                    </div>
                    <div class="agent-count" id="agentCount">110 Active</div>
                </div>
                
                <!-- Holographic Agent Display -->
                <div class="holographic-display">
                    <div class="holographic-platform"></div>
                    <div class="agent-hologram" id="agentHologram">
                        <div class="agent-silhouette"></div>
                        <div class="agent-scan-line"></div>
                        <div class="agent-name" id="hologramName">Lily</div>
                        <div class="agent-id" id="hologramId">NEXUS-AGENT-001</div>
                        <div class="agent-function" id="hologramFunction">context_weaver()</div>
                    </div>
                </div>
                
                <!-- Agent Carousel -->
                <div class="agent-list-container">
                    <div class="list-controls">
                        <div class="list-control" id="prevBtn">
                            <i class="fas fa-chevron-left"></i>
                        </div>
                        <div class="list-control" id="nextBtn">
                            <i class="fas fa-chevron-right"></i>
                        </div>
                    </div>
                    <div class="agent-list" id="agentList">
                        <!-- Agent cards will be populated here -->
                    </div>
                </div>
            </div>

            <!-- Sidebar -->
            <div class="sidebar">
                <!-- Metrics Panel -->
                <div class="metrics-panel">
                    <div class="metrics-header">
                        <div class="metrics-title">
                            <i class="fas fa-chart-line"></i> System Metrics
                        </div>
                    </div>
                    <div class="metrics-content">
                        <div class="metric-item">
                            <div class="metric-label">Active Agents</div>
                            <div class="metric-value" id="activeAgentsCount">110</div>
                        </div>
                        <div class="metric-item">
                            <div class="metric-label">Tasks Completed</div>
                            <div class="metric-value" id="tasksCompleted">8,547</div>
                        </div>
                        <div class="metric-item">
                            <div class="metric-label">System Load</div>
                            <div class="metric-value" id="systemLoad">73%</div>
                        </div>
                        <div class="metric-item">
                            <div class="metric-label">Efficiency</div>
                            <div class="metric-value" id="efficiency">94%</div>
                        </div>
                    </div>
                </div>

                <!-- Agent Info Panel -->
                <div class="agent-info-panel">
                    <div class="metrics-header">
                        <div class="metrics-title">
                            <i class="fas fa-info-circle"></i> Agent Details
                        </div>
                    </div>
                    <div class="agent-info-content" id="agentInfoContent">
                        <!-- Agent details will be populated here -->
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Agent Data
        const agents = [
            { id: 'NEXUS-AGENT-001', name: 'Lily', function: 'context_weaver()', category: 'Core Cognitive & Orchestration', 
              description: 'Advanced context weaving agent responsible for connecting information across knowledge domains.',
              tools: ['Transformers.js', 'TensorFlow.js', 'LangChain', 'WebWorkers'],
              performance: 92, efficiency: 87, avatar: '👩‍💼' },
            { id: 'NEXUS-AGENT-002', name: 'Gabriel', function: 'spec_enforcer()', category: 'Core Cognitive & Orchestration',
              description: 'Ensures all data and operations conform to predefined specifications and standards.',
              tools: ['JSON Schema', 'Validator.js', 'OpenAPI', 'TypeScript'],
              performance: 89, efficiency: 94, avatar: '👨‍💻' },
            { id: 'NEXUS-AGENT-003', name: 'Adam', function: 'knowledge_grafter()', category: 'Core Cognitive & Orchestration',
              description: 'Integrates new knowledge and information into existing knowledge structures.',
              tools: ['Vector DB', 'GraphQL', 'Semantic Networks', 'LLM Integration'],
              performance: 85, efficiency: 91, avatar: '🧠' },
            { id: 'NEXUS-AGENT-004', name: 'Avery', function: 'polyglot_compiler()', category: 'Code & Package Management',
              description: 'Translates and compiles code between multiple programming languages.',
              tools: ['Babel', 'TypeScript', 'WebAssembly', 'Rust'],
              performance: 96, efficiency: 88, avatar: '🔄' },
            { id: 'NEXUS-AGENT-005', name: 'Emma', function: 'smart_fetch()', category: 'Web & Network Operations',
              description: 'Optimized data fetching with intelligent caching and retry strategies.',
              tools: ['Fetch API', 'Service Workers', 'Caching', 'WebSockets'],
              performance: 91, efficiency: 89, avatar: '📡' }
        ];

        // Create more agents to populate the list (total 110)
        const agentNames = ['Sophie', 'James', 'Oliver', 'Charlotte', 'Ethan', 'Amelia', 'Mia', 'Noah', 'Harper', 'Lucas', 'Evelyn', 'Mason'];
        const agentFunctions = [
            'cdn_preflight()', 'rpc_optimizer()', 'cors_ninja()', 'payload_auditor()', 'dep_resolver()',
            'bundle_surgeon()', 'wasm_transmute()', 'typescript_evolver()', 'package_alchemist()', 'shadow_dom_proxy()',
            'a11y_enforcer()', 'render_phantom()', 'storage_migrator()', 'service_worker_orchestrator()', 'web_crypto_agent()',
            'data_lake_miner()', 'privacy_sentinel()', 'schema_breeder()', 'vector_embedder()', 'perf_necromancer()'
        ];
        const categories = [
            'Web & Network Operations', 'Browser & DOM Operations', 'Security & Compliance',
            'Data Processing & Management', 'Performance & Monitoring', 'Specialized API/Resource Interaction',
            'Code & Package Management'
        ];
        const avatars = ['👩‍💻', '👨‍💻', '🔍', '🛠️', '📊', '🔒', '📱', '⚙️', '🧪', '📚', '🌐', '🤖', '📈', '📡', '🔧'];

        // Extend agents array
        for (let i = 5; i < 110; i++) {
            const nameIndex = i % agentNames.length;
            const functionIndex = i % agentFunctions.length;
            const categoryIndex = i % categories.length;
            const avatarIndex = i % avatars.length;
            
            agents.push({
                id: `NEXUS-AGENT-${(i+1).toString().padStart(3, '0')}`,
                name: agentNames[nameIndex],
                function: agentFunctions[functionIndex],
                category: categories[categoryIndex],
                description: `Advanced agent specialized in ${agentFunctions[functionIndex].replace('()', '')} operations.`,
                tools: ['WebWorkers', 'TensorFlow.js', 'IndexedDB', 'ServiceWorkerAPI'].sort(() => Math.random() - 0.5),
                performance: Math.floor(Math.random() * 15) + 80,
                efficiency: Math.floor(Math.random() * 15) + 80,
                avatar: avatars[avatarIndex]
            });
        }

        // Initialize page
        document.addEventListener('DOMContentLoaded', function() {
            // Create particles
            createParticles();
            
            // Populate agent list
            populateAgentList();
            
            // Initialize event listeners
            initEventListeners();
            
            // Select first agent
            selectAgent(0);
            
            // Hide loading overlay
            setTimeout(() => {
                document.getElementById('loadingOverlay').classList.add('hidden');
            }, 1500);
            
            // Update metrics periodically
            setInterval(updateMetrics, 5000);
        });

        // Create floating particles
        function createParticles() {
            const container = document.getElementById('particles');
            
            // Create initial particles
            for (let i = 0; i < 20; i++) {
                createParticle();
            }
            
            // Continue creating particles over time
            setInterval(createParticle, 1000);
            
            function createParticle() {
                const particle = document.createElement('div');
                particle.className = 'particle';
                particle.style.left = Math.random() * 100 + '%';
                particle.style.animationDelay = Math.random() * 20 + 's';
                particle.style.animationDuration = (Math.random() * 10 + 15) + 's';
                container.appendChild(particle);

                setTimeout(() => {
                    particle.remove();
                }, 25000);
            }
        }

        // Populate agent list
        function populateAgentList() {
            const agentList = document.getElementById('agentList');
            
            agents.forEach((agent, index) => {
                const card = document.createElement('div');
                card.className = 'agent-card';
                card.dataset.index = index;
                
                if (index === 0) {
                    card.classList.add('active');
                }
                
                card.innerHTML = `
                    <div class="card-avatar">${agent.avatar}</div>
                    <div class="card-name">${agent.name}</div>
                    <div class="card-id">${agent.id}</div>
                `;
                
                card.addEventListener('click', () => selectAgent(index));
                agentList.appendChild(card);
            });
        }

        // Select an agent
        function selectAgent(index) {
            const agent = agents[index];
            
            // Update hologram
            document.getElementById('hologramName').textContent = agent.name;
            document.getElementById('hologramId').textContent = agent.id;
            document.getElementById('hologramFunction').textContent = agent.function;
            
            // Update agent cards
            document.querySelectorAll('.agent-card').forEach((card, i) => {
                if (i === index) {
                    card.classList.add('active');
                    // Scroll card into view
                    card.scrollIntoView({ behavior: 'smooth', block: 'nearest', inline: 'center' });
                } else {
                    card.classList.remove('active');
                }
            });
            
            // Update agent info
            const infoContent = document.getElementById('agentInfoContent');
            infoContent.innerHTML = `
                <div class="info-category">${agent.category}</div>
                
                <div class="info-section">
                    <div class="info-description">${agent.description}</div>
                </div>
                
                <div class="info-section">
                    <h4>Primary Function</h4>
                    <div class="tool-tag">${agent.function}</div>
                </div>
                
                <div class="info-section">
                    <h4>Tools & Technologies</h4>
                    ${agent.tools.map(tool => `<span class="tool-tag">${tool}</span>`).join('')}
                </div>
                
                <div class="performance-section">
                    <h4>Performance: ${agent.performance}%</h4>
                    <div class="performance-bar">
                        <div class="performance-fill" style="width: ${agent.performance}%"></div>
                    </div>
                </div>
                
                <div class="performance-section">
                    <h4>Efficiency: ${agent.efficiency}%</h4>
                    <div class="performance-bar">
                        <div class="performance-fill" style="width: ${agent.efficiency}%"></div>
                    </div>
                </div>
            `;
        }

        // Initialize event listeners
        function initEventListeners() {
            // List navigation
            document.getElementById('prevBtn').addEventListener('click', () => {
                const activeCard = document.querySelector('.agent-card.active');
                const currentIndex = parseInt(activeCard.dataset.index);
                const prevIndex = currentIndex > 0 ? currentIndex - 1 : agents.length - 1;
                selectAgent(prevIndex);
            });
            
            document.getElementById('nextBtn').addEventListener('click', () => {
                const activeCard = document.querySelector('.agent-card.active');
                const currentIndex = parseInt(activeCard.dataset.index);
                const nextIndex = currentIndex < agents.length - 1 ? currentIndex + 1 : 0;
                selectAgent(nextIndex);
            });
            
            // Command buttons
            document.getElementById('addAgentBtn').addEventListener('click', () => {
                alert('Add Agent functionality is coming soon!');
            });
            
            document.getElementById('removeAgentBtn').addEventListener('click', () => {
                alert('Remove Agent functionality is coming soon!');
            });
            
            document.getElementById('assignTaskBtn').addEventListener('click', () => {
                alert('Assign Task functionality is coming soon!');
            });
            
            document.getElementById('linkAgentsBtn').addEventListener('click', () => {
                alert('Link Agents functionality is coming soon!');
            });
            
            // Keyboard navigation
            document.addEventListener('keydown', function(event) {
                switch (event.key) {
                    case 'ArrowLeft':
                        document.getElementById('prevBtn').click();
                        break;
                    case 'ArrowRight':
                        document.getElementById('nextBtn').click();
                        break;
                }
            });
        }

        // Update metrics
        function updateMetrics() {
            document.getElementById('activeAgentsCount').textContent = Math.floor(Math.random() * 10) + 105;
            document.getElementById('tasksCompleted').textContent = (8000 + Math.floor(Math.random() * 1000)).toLocaleString();
            document.getElementById('systemLoad').textContent = (60 + Math.floor(Math.random() * 30)) + '%';
            document.getElementById('efficiency').textContent = (85 + Math.floor(Math.random() * 15)) + '%';
            document.getElementById('agentCount').textContent = document.getElementById('activeAgentsCount').textContent + ' Active';
        }
    </script>
</body>
</html>