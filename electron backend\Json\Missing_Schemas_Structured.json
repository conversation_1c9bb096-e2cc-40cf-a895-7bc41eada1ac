[{"session_id": "uuid", "prompt": "What is the best way to initiate multi-GUI rendering?", "response": "By instantiating draggable containers from brain region triggers...", "token_usage": 224, "context_scope": ["agents", "gui_core", "worker_status"], "timestamp": "ISO8601", "embedding_vector": "[...1536 floats]"}, {"entry_id": "uuid", "region": "Agent Center", "gui_action": "open-minimized", "html_component": "Agent <PERSON>'s Agent Center.html", "timestamp": "ISO8601", "user_command": "show me workers and tasks"}, {"@type": "<PERSON><PERSON><PERSON>", "name": "llmInitializationState", "description": "Tracks boot sequence, memory hydration, and context priming events for the current LLM session.", "fields": {"sessionID": "string", "bootTimestamp": "datetime", "contextLoaded": "boolean", "initVectorHash": "string", "llmRoleActivated": "string", "linkedAgents": ["AgentID"]}}, {"@type": "<PERSON><PERSON><PERSON>", "name": "llmDecisionChain", "description": "Represents the ordered stack of decisions made by the LLM in response to agent/user requests.", "fields": {"decisionID": "string", "reasoning": "string", "triggerSource": "enum[user, agent, internal]", "confidenceScore": "float", "dependencies": ["decisionID"], "timestamp": "datetime"}}, {"@type": "<PERSON><PERSON><PERSON>", "name": "llmReinforcementLog", "description": "Logs internal or external feedback signals that reinforce or negate previous decisions.", "fields": {"reinforcementID": "string", "linkedDecision": "decisionID", "feedbackSource": "enum[user, system, agent]", "signal": "enum[positive, negative, neutral]", "adjustment": "float", "timestamp": "datetime"}}, {"@type": "<PERSON><PERSON><PERSON>", "name": "llmSelfCritiqueHistory", "description": "A reflective log of the LLM's self-evaluation routines.", "fields": {"critiqueID": "string", "triggerContext": "string", "issuesIdentified": ["string"], "correctionsProposed": ["string"], "appliedFix": "boolean", "timestamp": "datetime"}}, {"@type": "<PERSON><PERSON><PERSON>", "name": "userSentimentProfile", "description": "Stores ongoing emotional signals, language patterns, and affective summaries for the current user.", "fields": {"userID": "string", "emotionalTone": "enum[neutral, positive, frustrated, confused, excited, skeptical]", "recentKeywords": ["string"], "interactionScore": "float", "volatilityScore": "float", "lastUpdated": "datetime"}}, {"@type": "<PERSON><PERSON><PERSON>", "name": "agentSpeechPattern", "description": "Controls Agent <PERSON>’s default tone, vocabulary, personality, and delivery style.", "fields": {"agentName": "string", "speechTone": "enum[professional, humorous, charming, empathetic, instructive]", "defaultGreeting": "string", "fallbackPhrase": "string", "signatureClosure": "string", "usesPauseBeforeReply": "boolean", "mirrorsUserTone": "boolean"}}, {"@type": "<PERSON><PERSON><PERSON>", "name": "conversationRelationalMap", "description": "Captures historical tone dynamics and mutual engagement metrics between <PERSON> and the user.", "fields": {"interactionID": "string", "userTone": "string", "agentTone": "string", "sentimentMatchScore": "float", "responseAdjustments": ["string"], "sessionHistoryReference": "string", "timestamp": "datetime"}}, {"@type": "<PERSON><PERSON><PERSON>", "name": "empathicResponseTriggers", "description": "Defines conditions and rules that trigger emotionally sensitive responses from Agent <PERSON>.", "fields": {"triggerPhrase": "string", "expectedEmotion": "string", "responseTemplate": "string", "toneRequired": "enum[gentle, direct, compassionate, clarifying]", "modulateVoice": "boolean"}}, {"@type": "<PERSON><PERSON><PERSON>", "name": "workerProfile", "description": "Metadata and active monitoring profile of each worker agent.", "fields": {"workerID": "string", "role": "enum[agent, assistant, tool, observer]", "assignedTools": ["string"], "taskCount": "integer", "successfulCompletions": "integer", "errorRate": "float", "lastCheckIn": "datetime", "performanceTier": "enum[A, B, C, D]"}}, {"@type": "<PERSON><PERSON><PERSON>", "name": "toolchainRegistry", "description": "Global listing of tools available to all agents and workers, with ownership and capabilities.", "fields": {"toolID": "string", "ownedBy": "string", "toolType": "enum[ai_model, ui_module, plugin, sensor, camera, audio, script]", "version": "string", "isOnline": "boolean", "latencyScore": "float"}}, {"@type": "<PERSON><PERSON><PERSON>", "name": "workerTaskLog", "description": "Chronological list of tasks undertaken by a worker or agent with linked metadata.", "fields": {"logID": "string", "workerID": "string", "taskID": "string", "taskType": "enum[LLM_response, GUI_command, sensor_activation, DB_write, user_dialogue]", "taskStatus": "enum[completed, failed, deferred, retried]", "startTime": "datetime", "endTime": "datetime", "attachedOutput": "string"}}, {"@type": "<PERSON><PERSON><PERSON>", "name": "agentWorkerSync", "description": "Describes how agents report to and coordinate with workers for joint task execution.", "fields": {"sessionID": "string", "agentID": "string", "workerID": "string", "checklistConfirmed": "boolean", "toolInventoryVerified": "boolean", "syncedAt": "datetime", "progressNote": "string"}}, {"@type": "<PERSON><PERSON><PERSON>", "name": "agentToLLMNotepadTransfer", "description": "Schema for how agents post reports into the To-Do List notepad for LLM parsing.", "fields": {"noteID": "string", "sessionID": "string", "reportedBy": "string", "content": "string", "timestamp": "datetime", "linkedWorkerID": "string", "priorityFlag": "boolean"}}, {"@type": "<PERSON><PERSON><PERSON>", "name": "systemHealthSignal", "description": "Tracks the health and responsiveness of each system component via heartbeats and signals.", "fields": {"componentID": "string", "status": "enum[healthy, degraded, offline]", "latency": "float", "volatility": "float", "lastHeartbeat": "datetime", "diagnosticFlag": "boolean"}}, {"@type": "<PERSON><PERSON><PERSON>", "name": "repairPolicyRegistry", "description": "Defines recovery strategies triggered when thresholds or anomalies are breached.", "fields": {"policyID": "string", "appliesTo": "string", "triggerCondition": "string", "repairSteps": ["string"], "requiresHuman": "boolean", "priority": "enum[low, medium, high, critical]"}}, {"@type": "<PERSON><PERSON><PERSON>", "name": "entropyDecayMap", "description": "Monitors cognitive drift and knowledge decay over time across vectors and memory fields.", "fields": {"vectorID": "string", "source": "enum[agentMemory, dbMemory, userHistory]", "decayRate": "float", "lastRefreshed": "datetime", "autoScrubFlag": "boolean"}}, {"@type": "<PERSON><PERSON><PERSON>", "name": "scrubBotActions", "description": "Self-executing cognitive hygiene processes that clean and restructure decayed or volatile nodes.", "fields": {"actionID": "string", "initiator": "string", "targetVectorID": "string", "repairType": "enum[neural_scrub, hyperedge_rewire, vector_resync]", "executedAt": "datetime", "successFlag": "boolean", "resourceConsumption": {"gpuUnits": "float", "cpuLoad": "float"}}}, {"@type": "<PERSON><PERSON><PERSON>", "name": "selfRepairJournal", "description": "Chronological log of all self-diagnostic and repair actions taken by Agent <PERSON>.", "fields": {"journalID": "string", "initiatedBy": "string", "repairChain": ["string"], "startTime": "datetime", "endTime": "datetime", "outcome": "string", "loggedBy": "string"}}, {"@type": "<PERSON><PERSON><PERSON>", "name": "llmInstructionQueue", "description": "Queue of tasks generated by the LLM and sent to agents or workers for execution.", "fields": {"taskID": "string", "issuedBy": "string", "recipientType": "enum[agent, worker]", "instruction": "string", "priority": "enum[low, medium, high, urgent]", "status": "enum[pending, in_progress, completed, failed]", "timestampIssued": "datetime", "deadline": "datetime"}}, {"@type": "<PERSON><PERSON><PERSON>", "name": "agentToDoSync", "description": "Tracks the synchronization of To-Do List items received and processed by Agents.", "fields": {"agentID": "string", "syncedTasks": ["string"], "lastSynced": "datetime", "syncStatus": "enum[synced, partial, error]"}}, {"@type": "<PERSON><PERSON><PERSON>", "name": "agentWorkerHandshake", "description": "Records interaction agreements between agents and workers on shared responsibilities.", "fields": {"handshakeID": "string", "agentID": "string", "workerID": "string", "taskShared": "string", "toolsConfirmed": ["string"], "timestamp": "datetime", "status": "enum[initiated, acknowledged, active, complete]"}}, {"@type": "<PERSON><PERSON><PERSON>", "name": "notepadEntry", "description": "A raw or structured note entry recorded by an Agent, Worker, or LLM.", "fields": {"noteID": "string", "author": "string", "origin": "enum[agent, worker, llm, user]", "content": "string", "linkedTasks": ["string"], "timestamp": "datetime", "structured": "boolean"}}, {"@type": "<PERSON><PERSON><PERSON>", "name": "structuredNoteFragment", "description": "An intelligent parsing of a notepad entry to identify actionable fragments.", "fields": {"fragmentID": "string", "parentNoteID": "string", "type": "enum[todo, decision, question, error, learning, instruction]", "text": "string", "agentIntent": "string", "confidenceScore": "float", "status": "enum[pending, converted, discarded]", "extractedBy": "string"}}, {"@type": "<PERSON><PERSON><PERSON>", "name": "noteConversionEvent", "description": "Tracks when the LLM converts an unstructured note into a structured object or To-Do.", "fields": {"eventID": "string", "sourceNoteID": "string", "convertedTo": "enum[task, prompt, schema, question]", "convertedBy": "string", "timestamp": "datetime", "conversionQuality": "float"}}, {"@type": "<PERSON><PERSON><PERSON>", "name": "noteReviewFeedback", "description": "User or LLM feedback on whether the note conversion was accurate or useful.", "fields": {"reviewID": "string", "reviewer": "string", "targetNoteID": "string", "feedbackText": "string", "score": "float", "timestamp": "datetime"}}, {"@type": "<PERSON><PERSON><PERSON>", "name": "contextualThreadCluster", "description": "Groups related notes and thoughts into conceptual threads across time.", "fields": {"clusterID": "string", "label": "string", "linkedNoteIDs": ["string"], "createdBy": "string", "semanticTags": ["string"], "status": "enum[active, archived, expanded]", "lastUpdated": "datetime"}}, {"@type": "<PERSON><PERSON><PERSON>", "name": "persistentMemoryUnit", "description": "Atomic memory entry with emotional weight and usage frequency.", "fields": {"memoryID": "string", "contextLabel": "string", "encodedText": "string", "timestamp": "datetime", "lastAccessed": "datetime", "emotionalValence": "float", "usageCount": "int", "relevanceScore": "float"}}, {"@type": "<PERSON><PERSON><PERSON>", "name": "memoryCluster", "description": "A conceptual grouping of memories around a topic or user pattern.", "fields": {"clusterID": "string", "topic": "string", "linkedMemoryIDs": ["string"], "clusterWeight": "float", "originatingAgent": "string", "status": "enum[active, archived, decaying]"}}, {"@type": "<PERSON><PERSON><PERSON>", "name": "autoMemoryExpansionPlan", "description": "Defines how and when <PERSON> should create new memory clusters.", "fields": {"planID": "string", "trigger": "enum[task_count, agent_interaction, event_importance]", "targetCluster": "string", "newFields": ["string"], "monitorInterval": "string", "expansionType": "enum[semantic, episodic, procedural]", "lastEvaluated": "datetime"}}, {"@type": "<PERSON><PERSON><PERSON>", "name": "microDatabaseIndex", "description": "Tracks sub-databases created by <PERSON>, LLMs, or tools for scoped memory.", "fields": {"dbID": "string", "title": "string", "scope": "string", "creator": "string", "createdOn": "datetime", "linkedSchemas": ["string"], "accessAgents": ["string"]}}, {"@type": "<PERSON><PERSON><PERSON>", "name": "knowledgeCrystallizationRecord", "description": "Tracks conversion of lived experience or multiple tasks into a high-trust core belief or logic.", "fields": {"recordID": "string", "sourceMemoryIDs": ["string"], "resultingConcept": "string", "trustScore": "float", "crystallizedOn": "datetime", "promotedBy": "string"}}, {"@type": "<PERSON><PERSON><PERSON>", "name": "taskDispatchRecord", "description": "Represents a dispatched instruction from the LLM to an agent or worker.", "fields": {"dispatchID": "string", "issuedBy": "enum[<PERSON><PERSON>, Agent, Worker]", "recipientID": "string", "taskCategory": "string", "taskPayload": "object", "timestampIssued": "datetime", "deadline": "datetime", "priority": "enum[low, normal, high, critical]", "status": "enum[pending, in_progress, complete, failed]", "assignedTools": ["string"]}}, {"@type": "<PERSON><PERSON><PERSON>", "name": "agentWorkerInteractionLog", "description": "Logs real-time interactions between agent and worker for task validation or coordination.", "fields": {"interactionID": "string", "agentID": "string", "workerID": "string", "interactionType": "enum[check_in, tool_request, error_report, update]", "content": "string", "timestamp": "datetime", "resolved": "boolean"}}, {"@type": "<PERSON><PERSON><PERSON>", "name": "taskExecutionFeedback", "description": "Structured feedback after a task is attempted, either by agent or worker.", "fields": {"feedbackID": "string", "taskID": "string", "submittedBy": "string", "summary": "string", "issuesDetected": ["string"], "resolutionStatus": "enum[unresolved, in_review, resolved]", "timestamp": "datetime"}}, {"@type": "<PERSON><PERSON><PERSON>", "name": "llmObservationEvent", "description": "LLM-generated passive observation about agent or worker behavior or task status.", "fields": {"eventID": "string", "observer": "string", "subjectID": "string", "context": "string", "observationText": "string", "timestamp": "datetime", "flaggedForReview": "boolean"}}, {"@type": "<PERSON><PERSON><PERSON>", "name": "workflowLinkageMap", "description": "Maps agent and worker responsibilities across the to-do system and tool use.", "fields": {"mapID": "string", "linkedAgentID": "string", "linkedWorkerID": "string", "taskRoute": ["taskDispatchRecord"], "currentPhase": "string", "toolsetUsed": ["string"], "status": "enum[syncing, diverged, stable, incomplete]"}}, {"@type": "<PERSON><PERSON><PERSON>", "name": "uiButtonActionSchema", "description": "Defines the role and behavior of each interactive button within the interface.", "fields": {"buttonID": "string", "label": "string", "triggerEvent": "enum[click, doubleClick, longPress, hold, rapidClick]", "linkedPrompt": "string", "defaultState": "enum[enabled, disabled, hidden]", "cooldownDurationMs": "integer", "debounceLogic": {"enabled": "boolean", "delayMs": "integer", "thresholdClicks": "integer"}, "priority": "enum[low, normal, critical]", "fallbackAction": "string"}}, {"@type": "<PERSON><PERSON><PERSON>", "name": "promptInvocationSchema", "description": "Links button press behavior or speech triggers to context-specific AI prompt activations.", "fields": {"promptID": "string", "source": "enum[button, voiceCommand, systemEvent]", "triggeredBy": "string", "invocationContext": "string", "targetAgent": "string", "template": "string", "priority": "enum[low, standard, interruptive]", "delayBeforeActivationMs": "integer", "requiresUserConfirmation": "boolean"}}, {"@type": "<PERSON><PERSON><PERSON>", "name": "buttonConflictResolutionSchema", "description": "Manages conflicting or simultaneous button interactions to ensure stable interface behavior.", "fields": {"conflictID": "string", "involvedButtons": ["string"], "conflictType": "enum[simultaneousPress, invalidSequence, rapidPressExceed]", "resolutionStrategy": "enum[ignore, debounce, prioritize, rollback, queue]", "resolvedByAgent": "boolean", "timestamp": "datetime"}}, {"@type": "<PERSON><PERSON><PERSON>", "name": "agentPromptBehaviorSchema", "description": "Describes how <PERSON> processes and responds to button-linked or speech-invoked prompts.", "fields": {"agentID": "string", "recognizedTrigger": "string", "expectedResponseType": "enum[text, voice, visual, action]", "responseComplexity": "enum[short, detailed, multiPhase]", "emotionalTone": "enum[professional, excited, motivational, calming]", "shouldConfirmBeforeResponding": "boolean", "shouldShowThinkingState": "boolean", "canCancelMidResponse": "boolean"}}, {"@type": "<PERSON><PERSON><PERSON>", "name": "buttonDiagnosticSchema", "description": "Allows system diagnostics to verify correct button operation across states and user inputs.", "fields": {"testID": "string", "buttonID": "string", "testType": "enum[clickValidation, visualCheck, speechResponseLink, failureInjection]", "expectedOutcome": "string", "actualOutcome": "string", "passed": "boolean", "errorDetails": "string", "timestamp": "datetime"}}]