```javascript
// AGENT LEE - SUPREME MASSIVE JS FILE
// Generated: [Current Date]
// WARNING: This is a combination of many files.
// Some parts are environment-specific (Electron main/preload, Service Worker).
// For production, consider proper module bundling.

(function() { // Outer scope

// Namespace for non-window assignments
window.AgentLee = window.AgentLee || {};
AgentLee.API = AgentLee.API || {};
AgentLee.Utils = AgentLee.Utils || {};

// --- START OF FILE Agent_Coordination_Schema.json ---
AgentLee.Agent_Coordination_Schema = [
  {
    "Object Store":"agent_registry",
    "Purpose":"Stores metadata about all agents (name, role, capabilities, status, heartbeat, location)."
  },
  {
    "Object Store":"worker_registry",
    "Purpose":"Stores metadata about all workers (status, availability, completed tasks, specialties)."
  },
  {
    "Object Store":"tool_inventory",
    "Purpose":"Tracks all available tools, their current usage, ownership, and state (assigned, broken, idle)."
  },
  {
    "Object Store":"task_routing_queue",
    "Purpose":"Queues instructions dispatched from LLMs and assigns tasks to agents and workers based on priority."
  },
  {
    "Object Store":"agent_worker_sync_log",
    "Purpose":"Logs communication between agents and workers\u2014who requested what, when, and what was confirmed."
  },
  {
    "Object Store":"todo_list_notepad",
    "Purpose":"Stores raw collaborative data and messages from agents\/workers; parsed and interpreted by the LLM."
  },
  {
    "Object Store":"structured_memory_archive",
    "Purpose":"Maintains finalized, fully structured logs of tasks, relationships, and summaries curated by the LLM."
  }
];
// --- END OF FILE Agent_Coordination_Schema.json ---

// --- START OF FILE Agent_Lee__Deep_Schema_Table.json ---
AgentLee.Agent_Lee__Deep_Schema_Table = [
  {
    "Object Store":"agents",
    "Purpose":"Tracks all agents with status, region, and type."
  },
  {
    "Object Store":"agent_workflows",
    "Purpose":"Defines tools, tasks, and sequences for agent actions."
  },
  {
    "Object Store":"tasks",
    "Purpose":"Represents all tasks with triggers and assignment."
  },
  {
    "Object Store":"workers",
    "Purpose":"Defines worker roles, types, and performance metrics."
  },
  {
    "Object Store":"llm_sessions",
    "Purpose":"Tracks contextual LLM interactions per session."
  },
  {
    "Object Store":"azr_nodes",
    "Purpose":"AZR memory nodes, linked vectors, and entropy scores."
  },
  {
    "Object Store":"execution_logs",
    "Purpose":"Logs all command executions, timestamps, and outputs."
  },
  {
    "Object Store":"healing_logs",
    "Purpose":"Self-repair reports with triggers and severity."
  },
  {
    "Object Store":"diagnostics",
    "Purpose":"Evaluations of each module\u2019s performance."
  },
  {
    "Object Store":"gui_registry",
    "Purpose":"Tracks visual GUI regions, positions, and states."
  },
  {
    "Object Store":"telemetry",
    "Purpose":"Operational and behavioral metrics (user and system)."
  },
  {
    "Object Store":"speech_styles",
    "Purpose":"Voice tone, pacing, emotional markers for Agent Lee."
  },
  {
    "Object Store":"engagement_signals",
    "Purpose":"User engagement heuristics like hesitations or bursts."
  },
  {
    "Object Store":"user_behavior",
    "Purpose":"Schema to track attitude, tone, hesitancy of users."
  },
  {
    "Object Store":"memory_fragments",
    "Purpose":"Stores episodic, semantic, procedural memory."
  },
  {
    "Object Store":"learning_models",
    "Purpose":"Lifelong learning metrics and embedded models."
  },
  {
    "Object Store":"local_db_refs",
    "Purpose":"Smaller databases created for LLMs and agents to reason independently."
  },
  {
    "Object Store":"knowledge_updates",
    "Purpose":"Incremental updates to knowledge and schema learning."
  },
  {
    "Object Store":"motivation_triggers",
    "Purpose":"Schema for what uplifts or frustrates users."
  },
  {
    "Object Store":"emotion_tracks",
    "Purpose":"Emotional state mapping from text, speech, behavior."
  },
  {
    "Object Store":"agent_moods",
    "Purpose":"Simulated moods based on context and diagnostic states."
  },
  {
    "Object Store":"reflection_protocols",
    "Purpose":"Agent Lee\u2019s visible pauses, thoughtfulness, and summarization handling."
  }
];
// --- END OF FILE Agent_Lee__Deep_Schema_Table.json ---

// --- START OF FILE Agent_Lee___System-Level_Behavior_Schema.json ---
AgentLee.Agent_Lee___System_Level_Behavior_Schema = [
  {
    "Object Store":"user_behavior_logs",
    "Purpose":"Logs detailed interactions such as clicks, typing, stutters, deletions, multi-inputs, and timing behavior. Tracks intent patterns."
  },
  {
    "Object Store":"input_conflict_resolver",
    "Purpose":"Handles overlapping inputs (e.g. multiple button presses, voice+click). Resolves conflicts through priority queues and debounce logic."
  },
  {
    "Object Store":"voice_input_analyzer",
    "Purpose":"Analyzes stutters, pauses, interruptions, and resumes. Differentiates between hesitation and completion based on waveform features."
  },
  {
    "Object Store":"error_diagnostics",
    "Purpose":"Logs hardware\/software errors (e.g., camera failure, failed fetches). Enables Agent Lee to describe and troubleshoot issues with user."
  },
  {
    "Object Store":"user_feedback_queue",
    "Purpose":"Stores user frustrations, error reports, or confusion markers based on inferred behavior (e.g., retrying search, button spam, rapid form deletes)."
  },
  {
    "Object Store":"contextual_response_cache",
    "Purpose":"Caches fallback prompts, re-explanations, and alternate UI guidance paths for uncertain or disrupted user behavior."
  },
  {
    "Object Store":"camera_health_monitor",
    "Purpose":"Continuously checks access\/response latency\/errors on camera usage. Triggers real-time suggestions or step-by-step troubleshooting UI."
  },
  {
    "Object Store":"ui_interrupt_tracker",
    "Purpose":"Detects abandoned actions (e.g., started typing a message and stopped), or UI hesitation to predict engagement drop-off or confusion."
  }
];
// --- END OF FILE Agent_Lee___System-Level_Behavior_Schema.json ---

// --- START OF FILE cognitive_events_fixed.json ---
AgentLee.cognitive_events_fixed = {
  "event_id": "uuid",
  "timestamp": "2025-06-14T21:01:30Z",
  "actor": "PHI-3",
  "event_type": "schema_validation",
  "related_task_id": "task-23",
  "outcome": "validated",
  "confidence": "0.982"
};
// --- END OF FILE cognitive_events_fixed.json ---

// --- START OF FILE Missing_Schemas_Structured.json ---
AgentLee.Missing_Schemas_Structured = [
  {
    "session_id": "uuid",
    "prompt": "What is the best way to initiate multi-GUI rendering?",
    "response": "By instantiating draggable containers from brain region triggers...",
    "token_usage": 224,
    "context_scope": [
      "agents",
      "gui_core",
      "worker_status"
    ],
    "timestamp": "ISO8601",
    "embedding_vector": "[...1536 floats]"
  },
  {
    "entry_id": "uuid",
    "region": "Agent Center",
    "gui_action": "open-minimized",
    "html_component": "Agent Lee's Agent Center.html",
    "timestamp": "ISO8601",
    "user_command": "show me workers and tasks"
  },
  {
    "@type": "Schema",
    "name": "llmInitializationState",
    "description": "Tracks boot sequence, memory hydration, and context priming events for the current LLM session.",
    "fields": {
      "sessionID": "string",
      "bootTimestamp": "datetime",
      "contextLoaded": "boolean",
      "initVectorHash": "string",
      "llmRoleActivated": "string",
      "linkedAgents": [
        "AgentID"
      ]
    }
  },
  {
    "@type": "Schema",
    "name": "llmDecisionChain",
    "description": "Represents the ordered stack of decisions made by the LLM in response to agent/user requests.",
    "fields": {
      "decisionID": "string",
      "reasoning": "string",
      "triggerSource": "enum[user, agent, internal]",
      "confidenceScore": "float",
      "dependencies": [
        "decisionID"
      ],
      "timestamp": "datetime"
    }
  },
  {
    "@type": "Schema",
    "name": "llmReinforcementLog",
    "description": "Logs internal or external feedback signals that reinforce or negate previous decisions.",
    "fields": {
      "reinforcementID": "string",
      "linkedDecision": "decisionID",
      "feedbackSource": "enum[user, system, agent]",
      "signal": "enum[positive, negative, neutral]",
      "adjustment": "float",
      "timestamp": "datetime"
    }
  },
  {
    "@type": "Schema",
    "name": "llmSelfCritiqueHistory",
    "description": "A reflective log of the LLM's self-evaluation routines.",
    "fields": {
      "critiqueID": "string",
      "triggerContext": "string",
      "issuesIdentified": [
        "string"
      ],
      "correctionsProposed": [
        "string"
      ],
      "appliedFix": "boolean",
      "timestamp": "datetime"
    }
  },
  {
    "@type": "Schema",
    "name": "userSentimentProfile",
    "description": "Stores ongoing emotional signals, language patterns, and affective summaries for the current user.",
    "fields": {
      "userID": "string",
      "emotionalTone": "enum[neutral, positive, frustrated, confused, excited, skeptical]",
      "recentKeywords": [
        "string"
      ],
      "interactionScore": "float",
      "volatilityScore": "float",
      "lastUpdated": "datetime"
    }
  },
  {
    "@type": "Schema",
    "name": "agentSpeechPattern",
    "description": "Controls Agent Lee\u2019s default tone, vocabulary, personality, and delivery style.",
    "fields": {
      "agentName": "string",
      "speechTone": "enum[professional, humorous, charming, empathetic, instructive]",
      "defaultGreeting": "string",
      "fallbackPhrase": "string",
      "signatureClosure": "string",
      "usesPauseBeforeReply": "boolean",
      "mirrorsUserTone": "boolean"
    }
  },
  {
    "@type": "Schema",
    "name": "conversationRelationalMap",
    "description": "Captures historical tone dynamics and mutual engagement metrics between Agent Lee and the user.",
    "fields": {
      "interactionID": "string",
      "userTone": "string",
      "agentTone": "string",
      "sentimentMatchScore": "float",
      "responseAdjustments": [
        "string"
      ],
      "sessionHistoryReference": "string",
      "timestamp": "datetime"
    }
  },
  {
    "@type": "Schema",
    "name": "empathicResponseTriggers",
    "description": "Defines conditions and rules that trigger emotionally sensitive responses from Agent Lee.",
    "fields": {
      "triggerPhrase": "string",
      "expectedEmotion": "string",
      "responseTemplate": "string",
      "toneRequired": "enum[gentle, direct, compassionate, clarifying]",
      "modulateVoice": "boolean"
    }
  },
  {
    "@type": "Schema",
    "name": "workerProfile",
    "description": "Metadata and active monitoring profile of each worker agent.",
    "fields": {
      "workerID": "string",
      "role": "enum[agent, assistant, tool, observer]",
      "assignedTools": [
        "string"
      ],
      "taskCount": "integer",
      "successfulCompletions": "integer",
      "errorRate": "float",
      "lastCheckIn": "datetime",
      "performanceTier": "enum[A, B, C, D]"
    }
  },
  {
    "@type": "Schema",
    "name": "toolchainRegistry",
    "description": "Global listing of tools available to all agents and workers, with ownership and capabilities.",
    "fields": {
      "toolID": "string",
      "ownedBy": "string",
      "toolType": "enum[ai_model, ui_module, plugin, sensor, camera, audio, script]",
      "version": "string",
      "isOnline": "boolean",
      "latencyScore": "float"
    }
  },
  {
    "@type": "Schema",
    "name": "workerTaskLog",
    "description": "Chronological list of tasks undertaken by a worker or agent with linked metadata.",
    "fields": {
      "logID": "string",
      "workerID": "string",
      "taskID": "string",
      "taskType": "enum[LLM_response, GUI_command, sensor_activation, DB_write, user_dialogue]",
      "taskStatus": "enum[completed, failed, deferred, retried]",
      "startTime": "datetime",
      "endTime": "datetime",
      "attachedOutput": "string"
    }
  },
  {
    "@type": "Schema",
    "name": "agentWorkerSync",
    "description": "Describes how agents report to and coordinate with workers for joint task execution.",
    "fields": {
      "sessionID": "string",
      "agentID": "string",
      "workerID": "string",
      "checklistConfirmed": "boolean",
      "toolInventoryVerified": "boolean",
      "syncedAt": "datetime",
      "progressNote": "string"
    }
  },
  {
    "@type": "Schema",
    "name": "agentToLLMNotepadTransfer",
    "description": "Schema for how agents post reports into the To-Do List notepad for LLM parsing.",
    "fields": {
      "noteID": "string",
      "sessionID": "string",
      "reportedBy": "string",
      "content": "string",
      "timestamp": "datetime",
      "linkedWorkerID": "string",
      "priorityFlag": "boolean"
    }
  },
  {
    "@type": "Schema",
    "name": "systemHealthSignal",
    "description": "Tracks the health and responsiveness of each system component via heartbeats and signals.",
    "fields": {
      "componentID": "string",
      "status": "enum[healthy, degraded, offline]",
      "latency": "float",
      "volatility": "float",
      "lastHeartbeat": "datetime",
      "diagnosticFlag": "boolean"
    }
  },
  {
    "@type": "Schema",
    "name": "repairPolicyRegistry",
    "description": "Defines recovery strategies triggered when thresholds or anomalies are breached.",
    "fields": {
      "policyID": "string",
      "appliesTo": "string",
      "triggerCondition": "string",
      "repairSteps": [
        "string"
      ],
      "requiresHuman": "boolean",
      "priority": "enum[low, medium, high, critical]"
    }
  },
  {
    "@type": "Schema",
    "name": "entropyDecayMap",
    "description": "Monitors cognitive drift and knowledge decay over time across vectors and memory fields.",
    "fields": {
      "vectorID": "string",
      "source": "enum[agentMemory, dbMemory, userHistory]",
      "decayRate": "float",
      "lastRefreshed": "datetime",
      "autoScrubFlag": "boolean"
    }
  },
  {
    "@type": "Schema",
    "name": "scrubBotActions",
    "description": "Self-executing cognitive hygiene processes that clean and restructure decayed or volatile nodes.",
    "fields": {
      "actionID": "string",
      "initiator": "string",
      "targetVectorID": "string",
      "repairType": "enum[neural_scrub, hyperedge_rewire, vector_resync]",
      "executedAt": "datetime",
      "successFlag": "boolean",
      "resourceConsumption": {
        "gpuUnits": "float",
        "cpuLoad": "float"
      }
    }
  },
  {
    "@type": "Schema",
    "name": "selfRepairJournal",
    "description": "Chronological log of all self-diagnostic and repair actions taken by Agent Lee.",
    "fields": {
      "journalID": "string",
      "initiatedBy": "string",
      "repairChain": [
        "string"
      ],
      "startTime": "datetime",
      "endTime": "datetime",
      "outcome": "string",
      "loggedBy": "string"
    }
  },
  {
    "@type": "Schema",
    "name": "llmInstructionQueue",
    "description": "Queue of tasks generated by the LLM and sent to agents or workers for execution.",
    "fields": {
      "taskID": "string",
      "issuedBy": "string",
      "recipientType": "enum[agent, worker]",
      "instruction": "string",
      "priority": "enum[low, medium, high, urgent]",
      "status": "enum[pending, in_progress, completed, failed]",
      "timestampIssued": "datetime",
      "deadline": "datetime"
    }
  },
  {
    "@type": "Schema",
    "name": "agentToDoSync",
    "description": "Tracks the synchronization of To-Do List items received and processed by Agents.",
    "fields": {
      "agentID": "string",
      "syncedTasks": [
        "string"
      ],
      "lastSynced": "datetime",
      "syncStatus": "enum[synced, partial, error]"
    }
  },
  {
    "@type": "Schema",
    "name": "agentWorkerHandshake",
    "description": "Records interaction agreements between agents and workers on shared responsibilities.",
    "fields": {
      "handshakeID": "string",
      "agentID": "string",
      "workerID": "string",
      "taskShared": "string",
      "toolsConfirmed": [
        "string"
      ],
      "timestamp": "datetime",
      "status": "enum[initiated, acknowledged, active, complete]"
    }
  },
  {
    "@type": "Schema",
    "name": "notepadEntry",
    "description": "A raw or structured note entry recorded by an Agent, Worker, or LLM.",
    "fields": {
      "noteID": "string",
      "author": "string",
      "origin": "enum[agent, worker, llm, user]",
      "content": "string",
      "linkedTasks": [
        "string"
      ],
      "timestamp": "datetime",
      "structured": "boolean"
    }
  },
  {
    "@type": "Schema",
    "name": "structuredNoteFragment",
    "description": "An intelligent parsing of a notepad entry to identify actionable fragments.",
    "fields": {
      "fragmentID": "string",
      "parentNoteID": "string",
      "type": "enum[todo, decision, question, error, learning, instruction]",
      "text": "string",
      "agentIntent": "string",
      "confidenceScore": "float",
      "status": "enum[pending, converted, discarded]",
      "extractedBy": "string"
    }
  },
  {
    "@type": "Schema",
    "name": "noteConversionEvent",
    "description": "Tracks when the LLM converts an unstructured note into a structured object or To-Do.",
    "fields": {
      "eventID": "string",
      "sourceNoteID": "string",
      "convertedTo": "enum[task, prompt, schema, question]",
      "convertedBy": "string",
      "timestamp": "datetime",
      "conversionQuality": "float"
    }
  },
  {
    "@type": "Schema",
    "name": "noteReviewFeedback",
    "description": "User or LLM feedback on whether the note conversion was accurate or useful.",
    "fields": {
      "reviewID": "string",
      "reviewer": "string",
      "targetNoteID": "string",
      "feedbackText": "string",
      "score": "float",
      "timestamp": "datetime"
    }
  },
  {
    "@type": "Schema",
    "name": "contextualThreadCluster",
    "description": "Groups related notes and thoughts into conceptual threads across time.",
    "fields": {
      "clusterID": "string",
      "label": "string",
      "linkedNoteIDs": [
        "string"
      ],
      "createdBy": "string",
      "semanticTags": [
        "string"
      ],
      "status": "enum[active, archived, expanded]",
      "lastUpdated": "datetime"
    }
  },
  {
    "@type": "Schema",
    "name": "persistentMemoryUnit",
    "description": "Atomic memory entry with emotional weight and usage frequency.",
    "fields": {
      "memoryID": "string",
      "contextLabel": "string",
      "encodedText": "string",
      "timestamp": "datetime",
      "lastAccessed": "datetime",
      "emotionalValence": "float",
      "usageCount": "int",
      "relevanceScore": "float"
    }
  },
  {
    "@type": "Schema",
    "name": "memoryCluster",
    "description": "A conceptual grouping of memories around a topic or user pattern.",
    "fields": {
      "clusterID": "string",
      "topic": "string",
      "linkedMemoryIDs": [
        "string"
      ],
      "clusterWeight": "float",
      "originatingAgent": "string",
      "status": "enum[active, archived, decaying]"
    }
  },
  {
    "@type": "Schema",
    "name": "autoMemoryExpansionPlan",
    "description": "Defines how and when Agent Lee should create new memory clusters.",
    "fields": {
      "planID": "string",
      "trigger": "enum[task_count, agent_interaction, event_importance]",
      "targetCluster": "string",
      "newFields": [
        "string"
      ],
      "monitorInterval": "string",
      "expansionType": "enum[semantic, episodic, procedural]",
      "lastEvaluated": "datetime"
    }
  },
  {
    "@type": "Schema",
    "name": "microDatabaseIndex",
    "description": "Tracks sub-databases created by Agent Lee, LLMs, or tools for scoped memory.",
    "fields": {
      "dbID": "string",
      "title": "string",
      "scope": "string",
      "creator": "string",
      "createdOn": "datetime",
      "linkedSchemas": [
        "string"
      ],
      "accessAgents": [
        "string"
      ]
    }
  },
  {
    "@type": "Schema",
    "name": "knowledgeCrystallizationRecord",
    "description": "Tracks conversion of lived experience or multiple tasks into a high-trust core belief or logic.",
    "fields": {
      "recordID": "string",
      "sourceMemoryIDs": [
        "string"
      ],
      "resultingConcept": "string",
      "trustScore": "float",
      "crystallizedOn": "datetime",
      "promotedBy": "string"
    }
  },
  {
    "@type": "Schema",
    "name": "taskDispatchRecord",
    "description": "Represents a dispatched instruction from the LLM to an agent or worker.",
    "fields": {
      "dispatchID": "string",
      "issuedBy": "enum[LLM, Agent, Worker]",
      "recipientID": "string",
      "taskCategory": "string",
      "taskPayload": "object",
      "timestampIssued": "datetime",
      "deadline": "datetime",
      "priority": "enum[low, normal, high, critical]",
      "status": "enum[pending, in_progress, complete, failed]",
      "assignedTools": [
        "string"
      ]
    }
  },
  {
    "@type": "Schema",
    "name": "agentWorkerInteractionLog",
    "description": "Logs real-time interactions between agent and worker for task validation or coordination.",
    "fields": {
      "interactionID": "string",
      "agentID": "string",
      "workerID": "string",
      "interactionType": "enum[check_in, tool_request, error_report, update]",
      "content": "string",
      "timestamp": "datetime",
      "resolved": "boolean"
    }
  },
  {
    "@type": "Schema",
    "name": "taskExecutionFeedback",
    "description": "Structured feedback after a task is attempted, either by agent or worker.",
    "fields": {
      "feedbackID": "string",
      "taskID": "string",
      "submittedBy": "string",
      "summary": "string",
      "issuesDetected": [
        "string"
      ],
      "resolutionStatus": "enum[unresolved, in_review, resolved]",
      "timestamp": "datetime"
    }
  },
  {
    "@type": "Schema",
    "name": "llmObservationEvent",
    "description": "LLM-generated passive observation about agent or worker behavior or task status.",
    "fields": {
      "eventID": "string",
      "observer": "string",
      "subjectID": "string",
      "context": "string",
      "observationText": "string",
      "timestamp": "datetime",
      "flaggedForReview": "boolean"
    }
  },
  {
    "@type": "Schema",
    "name": "workflowLinkageMap",
    "description": "Maps agent and worker responsibilities across the to-do system and tool use.",
    "fields": {
      "mapID": "string",
      "linkedAgentID": "string",
      "linkedWorkerID": "string",
      "taskRoute": [
        "taskDispatchRecord"
      ],
      "currentPhase": "string",
      "toolsetUsed": [
        "string"
      ],
      "status": "enum[syncing, diverged, stable, incomplete]"
    }
  },
  {
    "@type": "Schema",
    "name": "uiButtonActionSchema",
    "description": "Defines the role and behavior of each interactive button within the interface.",
    "fields": {
      "buttonID": "string",
      "label": "string",
      "triggerEvent": "enum[click, doubleClick, longPress, hold, rapidClick]",
      "linkedPrompt": "string",
      "defaultState": "enum[enabled, disabled, hidden]",
      "cooldownDurationMs": "integer",
      "debounceLogic": {
        "enabled": "boolean",
        "delayMs": "integer",
        "thresholdClicks": "integer"
      },
      "priority": "enum[low, normal, critical]",
      "fallbackAction": "string"
    }
  },
  {
    "@type": "Schema",
    "name": "promptInvocationSchema",
    "description": "Links button press behavior or speech triggers to context-specific AI prompt activations.",
    "fields": {
      "promptID": "string",
      "source": "enum[button, voiceCommand, systemEvent]",
      "triggeredBy": "string",
      "invocationContext": "string",
      "targetAgent": "string",
      "template": "string",
      "priority": "enum[low, standard, interruptive]",
      "delayBeforeActivationMs": "integer",
      "requiresUserConfirmation": "boolean"
    }
  },
  {
    "@type": "Schema",
    "name": "buttonConflictResolutionSchema",
    "description": "Manages conflicting or simultaneous button interactions to ensure stable interface behavior.",
    "fields": {
      "conflictID": "string",
      "involvedButtons": [
        "string"
      ],
      "conflictType": "enum[simultaneousPress, invalidSequence, rapidPressExceed]",
      "resolutionStrategy": "enum[ignore, debounce, prioritize, rollback, queue]",
      "resolvedByAgent": "boolean",
      "timestamp": "datetime"
    }
  },
  {
    "@type": "Schema",
    "name": "agentPromptBehaviorSchema",
    "description": "Describes how Agent Lee processes and responds to button-linked or speech-invoked prompts.",
    "fields": {
      "agentID": "string",
      "recognizedTrigger": "string",
      "expectedResponseType": "enum[text, voice, visual, action]",
      "responseComplexity": "enum[short, detailed, multiPhase]",
      "emotionalTone": "enum[professional, excited, motivational, calming]",
      "shouldConfirmBeforeResponding": "boolean",
      "shouldShowThinkingState": "boolean",
      "canCancelMidResponse": "boolean"
    }
  },
  {
    "@type": "Schema",
    "name": "buttonDiagnosticSchema",
    "description": "Allows system diagnostics to verify correct button operation across states and user inputs.",
    "fields": {
      "testID": "string",
      "buttonID": "string",
      "testType": "enum[clickValidation, visualCheck, speechResponseLink, failureInjection]",
      "expectedOutcome": "string",
      "actualOutcome": "string",
      "passed": "boolean",
      "errorDetails": "string",
      "timestamp": "datetime"
    }
  }
];
// --- END OF FILE Missing_Schemas_Structured.json ---

// --- START OF FILE Schema_Overview_for_AgentLee_DB___ToDo_Enhancement.json ---
AgentLee.Schema_Overview_for_AgentLee_DB___ToDo_Enhancement = [
  {
    "Schema Name":"AgentCoreSchema",
    "Purpose":"Tracks agent roles, tools, energy, task flow"
  },
  {
    "Schema Name":"WorkerBehaviorSchema",
    "Purpose":"Captures worker performance, interaction cycles"
  },
  {
    "Schema Name":"LLMLifecycleSchema",
    "Purpose":"Manages LLM learning, sync, and feedback loops"
  },
  {
    "Schema Name":"TaskMemorySchema",
    "Purpose":"Logs agent+worker actions on ToDo notepad"
  },
  {
    "Schema Name":"HealingMatrixSchema",
    "Purpose":"Self-repair rules for data\/model consistency"
  },
  {
    "Schema Name":"ButtonLogicSchema",
    "Purpose":"Detects abnormal usage patterns on UI buttons"
  },
  {
    "Schema Name":"CameraDiagnosticsSchema",
    "Purpose":"Monitors camera usage & user issues"
  },
  {
    "Schema Name":"SpeechPauseSchema",
    "Purpose":"Handles user hesitation, stutter, double-actions"
  },
  {
    "Schema Name":"AgentWorkerBridgeSchema",
    "Purpose":"Ensures agent-worker collaboration"
  },
  {
    "Schema Name":"StructuredFallbackSchema",
    "Purpose":"Allows switching between structured\/unstructured task logs"
  },
  {
    "Schema Name":"PromptDirectiveSchema",
    "Purpose":"Stores LLM\/agent fallback prompts"
  },
  {
    "Schema Name":"SMSCallSchema",
    "Purpose":"Handles user phone\/SMS with failover triggers"
  },
  {
    "Schema Name":"InputConflictSchema",
    "Purpose":"Captures triple-clicks, typing + stutter + delete cycles"
  },
  {
    "Schema Name":"MicroDatabaseSchema",
    "Purpose":"Supports agents\/LLMs creating micro knowledge stores"
  }
];
// --- END OF FILE Schema_Overview_for_AgentLee_DB___ToDo_Enhancement.json ---

// --- START OF FILE worker_status_fixed.json ---
AgentLee.worker_status_fixed = {
  "worker_id": "uuid",
  "name": "SchemaCompiler-1",
  "type": "Database Parser",
  "current_job": "Convert TXT to IndexedDB Schema",
  "status": "running",
  "linked_llm": "PHI-3",
  "output_format": "JSON Schema",
  "gui_card_id": "worker_4a"
};
// --- END OF FILE worker_status_fixed.json ---

// --- START OF FILE llm_agents.js ---
// This is detailed LLM agent data, distinct from the llmAgents object used in cognitive-matrix.js
AgentLee.DetailedLLMAgentsData = {
    "phi3": {
        "name": "Phi-3",
        "role": "Quick Thinker",
        "color": "#2196F3",
        "avatar": "https://img.icons8.com/fluency/96/000000/brain.png",
        "description": "Phi-3 is a specialized language model that excels at rapid structure proposals and schema processing. It's designed to quickly analyze problems and generate initial frameworks.",
        "status": "Online",
        "manufacturer": "Microsoft",
        "parameters": "3.8 Billion",
        "specialization": "Fast reasoning, structure generation, initial analysis",
        "performance": {
            "speed": 95,
            "accuracy": 85,
            "efficiency": 90
        },
        "tools": [
            {
                "name": "Schema Analyzer",
                "description": "Analyzes complex data structures and identifies patterns and relationships."
            },
            {
                "name": "Framework Generator",
                "description": "Rapidly generates structural frameworks for problem-solving approaches."
            },
            {
                "name": "Quick Reasoner",
                "description": "Performs fast logical reasoning to provide initial solutions."
            }
        ],
        "workflow": [
            {
                "step": "Problem Analysis",
                "description": "Rapidly analyze the input problem to identify key components and requirements."
            },
            {
                "step": "Structure Proposal",
                "description": "Generate a comprehensive structural framework for approaching the problem."
            },
            {
                "step": "Schema Integration",
                "description": "Integrate the proposed structure with existing schemas in the system."
            },
            {
                "step": "Framework Delivery",
                "description": "Deliver the structured approach to the Problem Solver (Llama 3.1B) for deep reasoning."
            }
        ],
        "example_tasks": [
            "Generate a hierarchical structure for data organization",
            "Propose a framework for solving multi-step problems",
            "Create a schema for representing complex relationships"
        ],
        "integration_points": [
            "Receives tasks from AZR Nucleus",
            "Sends structural frameworks to Llama 3.1B",
            "Coordinates with Gemini for schema retrieval"
        ]
    },
    "llama": {
        "name": "Llama 3.1B",
        "role": "Problem Solver",
        "color": "#FF9800",
        "avatar": "https://img.icons8.com/fluency/96/000000/artificial-intelligence.png",
        "description": "Llama 3.1B is a powerful reasoning engine specialized in deep problem-solving and logical analysis. It takes structured frameworks and performs comprehensive reasoning to develop solutions.",
        "status": "Online",
        "manufacturer": "Meta AI",
        "parameters": "7 Billion",
        "specialization": "Deep reasoning, problem-solving, logical inference",
        "performance": {
            "speed": 80,
            "accuracy": 92,
            "efficiency": 88
        },
        "tools": [
            {
                "name": "Deep Reasoner",
                "description": "Performs multi-step logical reasoning to solve complex problems."
            },
            {
                "name": "Inference Engine",
                "description": "Draws logical inferences from premises to reach conclusions."
            },
            {
                "name": "Solution Optimizer",
                "description": "Optimizes solutions for efficiency and effectiveness."
            }
        ],
        "workflow": [
            {
                "step": "Framework Intake",
                "description": "Receive the structural framework from Phi-3 for the problem at hand."
            },
            {
                "step": "Deep Analysis",
                "description": "Perform comprehensive analysis of the problem using the provided framework."
            },
            {
                "step": "Solution Development",
                "description": "Develop a detailed solution through logical reasoning and inference."
            },
            {
                "step": "Verification Preparation",
                "description": "Prepare the solution for verification by Qwen."
            }
        ],
        "example_tasks": [
            "Solve multi-step logical problems",
            "Analyze complex systems for optimization",
            "Develop comprehensive solutions to structured problems"
        ],
        "integration_points": [
            "Receives frameworks from Phi-3",
            "Sends solutions to Qwen for verification",
            "Reports progress to AZR Nucleus"
        ]
    },
    "qwen": {
        "name": "Qwen",
        "role": "Verifier",
        "color": "#4CAF50",
        "avatar": "https://img.icons8.com/fluency/96/000000/check-all.png",
        "description": "Qwen is a specialized verification model that ensures logical consistency and accuracy of solutions. It acts as a quality control system within the cognitive matrix.",
        "status": "Online",
        "manufacturer": "Alibaba Cloud",
        "parameters": "1.8 Billion",
        "specialization": "Verification, validation, logical consistency checking",
        "performance": {
            "speed": 85,
            "accuracy": 97,
            "efficiency": 92
        },
        "tools": [
            {
                "name": "Consistency Checker",
                "description": "Verifies logical consistency across multi-step reasoning processes."
            },
            {
                "name": "Error Detector",
                "description": "Identifies logical errors, gaps, and inconsistencies in solutions."
            },
            {
                "name": "Confidence Scorer",
                "description": "Assigns confidence scores to solutions based on logical validity."
            }
        ],
        "workflow": [
            {
                "step": "Solution Reception",
                "description": "Receive the proposed solution from Llama 3.1B."
            },
            {
                "step": "Logical Validation",
                "description": "Verify the logical consistency and validity of each step in the solution."
            },
            {
                "step": "Error Detection",
                "description": "Identify any errors, gaps, or inconsistencies in the reasoning process."
            },
            {
                "step": "Confidence Assignment",
                "description": "Assign a confidence score to the solution and prepare for archiving."
            }
        ],
        "example_tasks": [
            "Verify logical consistency of complex reasoning chains",
            "Validate solutions against known constraints",
            "Detect errors in multi-step problem-solving approaches"
        ],
        "integration_points": [
            "Receives solutions from Llama 3.1B",
            "Sends verified results to Gemini for archiving",
            "Reports verification status to AZR Nucleus"
        ]
    },
    "gemini": {
        "name": "Gemini",
        "role": "Memory",
        "color": "#9C27B0",
        "avatar": "https://img.icons8.com/fluency/96/000000/database.png",
        "description": "Gemini serves as the long-term memory system of the cognitive matrix, archiving solutions, reasoning patterns, and knowledge for future reference and learning.",
        "status": "Online",
        "manufacturer": "Google DeepMind",
        "parameters": "5.5 Billion",
        "specialization": "Memory management, knowledge archival, pattern recognition",
        "performance": {
            "speed": 88,
            "accuracy": 94,
            "efficiency": 95
        },
        "tools": [
            {
                "name": "Vector Archive",
                "description": "Stores reasoning patterns and solutions in a retrievable vector format."
            },
            {
                "name": "Pattern Recognizer",
                "description": "Identifies similarities between current and past problems and solutions."
            },
            {
                "name": "Knowledge Indexer",
                "description": "Creates searchable indexes of archived knowledge for efficient retrieval."
            }
        ],
        "workflow": [
            {
                "step": "Result Reception",
                "description": "Receive verified solutions and reasoning chains from Qwen."
            },
            {
                "step": "Pattern Analysis",
                "description": "Analyze the solution for patterns that can be abstracted and stored."
            },
            {
                "step": "Vector Encoding",
                "description": "Encode the solution and reasoning patterns into vector representations."
            },
            {
                "step": "Memory Integration",
                "description": "Integrate the new knowledge into the existing memory structure."
            }
        ],
        "example_tasks": [
            "Archive verified solutions for future reference",
            "Recognize patterns across multiple problem-solving instances",
            "Retrieve relevant past solutions for similar new problems"
        ],
        "integration_points": [
            "Receives verified results from Qwen",
            "Provides contextual memory to Phi-3 for structure proposals",
            "Reports archival status to AZR Nucleus"
        ]
    },
    "echo": {
        "name": "Echo",
        "role": "Interface",
        "color": "#00BCD4",
        "avatar": "https://img.icons8.com/fluency/96/000000/communicate.png",
        "description": "Echo serves as the interface between the cognitive matrix and external systems, handling input/output operations and translating between different formats and protocols.",
        "status": "Online",
        "manufacturer": "Agent Lee Systems",
        "parameters": "1.2 Billion",
        "specialization": "Interface management, I/O operations, communication",
        "performance": {
            "speed": 96,
            "accuracy": 90,
            "efficiency": 93
        },
        "tools": [
            {
                "name": "Format Translator",
                "description": "Translates between different data formats and communication protocols."
            },
            {
                "name": "I/O Manager",
                "description": "Manages input and output operations between the cognitive matrix and external systems."
            },
            {
                "name": "Communication Router",
                "description": "Routes communications to appropriate systems and channels."
            }
        ],
        "workflow": [
            {
                "step": "Input Reception",
                "description": "Receive input from external systems and user interfaces."
            },
            {
                "step": "Format Translation",
                "description": "Translate the input into a format usable by the cognitive matrix."
            },
            {
                "step": "Task Routing",
                "description": "Route the translated input to the appropriate component of the matrix."
            },
            {
                "step": "Response Delivery",
                "description": "Deliver responses back to external systems in appropriate formats."
            }
        ],
        "example_tasks": [
            "Interface with user input systems",
            "Translate between different data formats",
            "Route communications between cognitive components"
        ],
        "integration_points": [
            "Interfaces with external systems and user interfaces",
            "Communicates with all components of the cognitive matrix",
            "Reports communication status to AZR Nucleus"
        ]
    },
    "azr": {
        "name": "AZR",
        "role": "Reasoning & Routing Nucleus",
        "color": "#1E3C72",
        "avatar": "https://img.icons8.com/fluency/96/000000/neural-connections.png",
        "description": "AZR (Absolute Zero Reasoner) serves as the central coordinating nucleus of the cognitive matrix, routing tasks between specialized LLMs and ensuring proper flow of information and processing.",
        "status": "Online",
        "manufacturer": "LeapLabTHU",
        "parameters": "Distributed Architecture",
        "specialization": "Task routing, coordination, reasoning orchestration",
        "performance": {
            "speed": 92,
            "accuracy": 96,
            "efficiency": 97
        },
        "tools": [
            {
                "name": "Task Router",
                "description": "Routes tasks to appropriate specialized LLMs based on task requirements."
            },
            {
                "name": "Flow Controller",
                "description": "Controls the flow of information and processing between LLMs."
            },
            {
                "name": "System Monitor",
                "description": "Monitors the status and performance of all components in the matrix."
            }
        ],
        "workflow": [
            {
                "step": "Task Reception",
                "description": "Receive tasks from Echo or internal components."
            },
            {
                "step": "Task Analysis",
                "description": "Analyze tasks to determine appropriate routing and processing steps."
            },
            {
                "step": "Process Orchestration",
                "description": "Orchestrate the processing flow through the specialized LLMs."
            },
            {
                "step": "Result Integration",
                "description": "Integrate results from multiple LLMs into coherent outputs."
            }
        ],
        "example_tasks": [
            "Route tasks to appropriate specialized LLMs",
            "Coordinate multi-step reasoning processes",
            "Monitor and optimize system performance"
        ],
        "integration_points": [
            "Central hub connecting all specialized LLMs",
            "Coordinates with Echo for external communications",
            "Interfaces with all components of the cognitive matrix"
        ]
    }
};
// --- END OF FILE llm_agents.js ---

// --- START OF FILE card_stacking.js ---
// ⏩ card_stacking.js
// Manages z-index ordering for draggable GUI cards
AgentLee.Utils.card_stacking = (function() {
    let zIndexCounter = 1000;
    return {
        bringToFront: function(cardElement) {
            cardElement.style.zIndex = zIndexCounter++;
        }
    };
})();
// --- END OF FILE card_stacking.js ---

// --- START OF FILE device_mirror.js ---
// ✅ device_mirror.js
// Device mirroring interface for Agent Lee's cross-device logic
AgentLee.Utils.device_mirror = {
    getDeviceInfo: function() {
        return {
            userAgent: navigator.userAgent,
            screen: {
                width: window.innerWidth,
                height: window.innerHeight
            },
            platform: navigator.platform,
            language: navigator.language
        };
    },
    syncToDevice: function(targetDevice, data) {
        console.log("[Device Mirror] Syncing to:", targetDevice);
        // Simulated sync
        return true;
    }
};
// --- END OF FILE device_mirror.js ---

// --- START OF FILE echo_engine_real.js ---
// ✅ echo_engine_real.js
// Echo LLM diagnostics module for Agent Lee
// Provides system health checks, memory audits, and real-time diagnostics
AgentLee.Utils.echo_engine_real = {
    runDiagnostics: function(memoryState, agentStatus) {
        console.log("[Echo] Running diagnostics...");
        return {
            memoryUsage: memoryState.usage,
            agentHealth: agentStatus.summary,
            timestamp: new Date().toISOString()
        };
    },
    logEvent: function(event) {
        const log = {
            event,
            time: new Date().toLocaleTimeString()
        };
        console.log("[Echo Log]", log);
        return log;
    }
};
// --- END OF FILE echo_engine_real.js ---

// --- START OF FILE gui_card_manager.js ---
// ✅ gui_card_manager.js
// Floating GUI card manager for Agent Lee interface
AgentLee.Utils.gui_card_manager = {
    spawnCard: function(id, content, x = 100, y = 100) {
        const card = document.createElement("div");
        card.id = id;
        card.className = "agent-card";
        card.style.position = "absolute";
        card.style.left = `${x}px`;
        card.style.top = `${y}px`;
        card.innerHTML = content;
        document.body.appendChild(card);
        return card;
    },
    destroyCard: function(id) {
        const card = document.getElementById(id);
        if (card) card.remove();
    }
};
// --- END OF FILE gui_card_manager.js ---

// --- START OF FILE mobile_touch_handler.js ---
// ⏩ mobile_touch_handler.js
// Enables mobile gesture support for GUI cards
AgentLee.Utils.mobile_touch_handler = {
    enableTouchDrag: function(element) {
        let offsetX, offsetY;

        element.addEventListener("touchstart", (e) => {
            const touch = e.touches[0];
            offsetX = touch.clientX - element.offsetLeft;
            offsetY = touch.clientY - element.offsetTop;
        });

        element.addEventListener("touchmove", (e) => {
            e.preventDefault();
            const touch = e.touches[0];
            element.style.left = `${touch.clientX - offsetX}px`;
            element.style.top = `${touch.clientY - offsetY}px`;
        });
    }
};
// --- END OF FILE mobile_touch_handler.js ---

// --- START OF FILE voice_engine.js ---
// ✅ voice_engine.js
// Text-to-speech and speech recognition engine for Agent Lee
AgentLee.Utils.voice_engine = (function() {
    let synth = window.speechSynthesis;
    return {
        speak: function(text) {
            if (!synth) return;
            const utterance = new SpeechSynthesisUtterance(text);
            synth.speak(utterance);
        },
        listen: function(callback) {
            const recognition = new (window.SpeechRecognition || window.webkitSpeechRecognition)();
            recognition.onresult = (event) => callback(event.results[0][0].transcript);
            recognition.start();
        }
    };
})();
// --- END OF FILE voice_engine.js ---

// --- START OF FILE automated_test_suite.js ---
/**
 * Agent Lee Automated Testing Suite
 * Comprehensive unit, integration, and end-to-end testing
 */
(function() {
    class AgentLeeTestSuite {
        constructor() {
            this.testResults = [];
            this.currentSuite = null;
            this.setupComplete = false;
            
            console.log('🧪 Agent Lee Test Suite initialized');
        }
        
        // ===== TEST FRAMEWORK =====
        
        async runAllTests() {
            console.log('🚀 Starting comprehensive test suite...');
            
            const suites = [
                { name: 'Database Tests', fn: () => this.runDatabaseTests() },
                { name: 'Event Bus Tests', fn: () => this.runEventBusTests() },
                { name: 'LLM Routing Tests', fn: () => this.runLLMRoutingTests() },
                { name: 'GUI Management Tests', fn: () => this.runGUITests() },
                { name: 'Performance Tests', fn: () => this.runPerformanceTests() },
                { name: 'Integration Tests', fn: () => this.runIntegrationTests() }
            ];
            
            for (const suite of suites) {
                await this.runTestSuite(suite.name, suite.fn);
            }
            
            return this.generateTestReport();
        }
        
        async runTestSuite(suiteName, testFunction) {
            this.currentSuite = suiteName;
            console.log(`\n📋 Running ${suiteName}...`);
            
            const suiteStart = performance.now();
            
            try {
                await testFunction();
                const duration = performance.now() - suiteStart;
                console.log(`✅ ${suiteName} completed in ${duration.toFixed(2)}ms`);
            } catch (error) {
                console.error(`❌ ${suiteName} failed:`, error);
                this.recordTest(`${suiteName} - Suite Error`, false, error.message);
            }
        }
        
        recordTest(testName, passed, details = null) {
            const result = {
                suite: this.currentSuite,
                test: testName,
                passed,
                details,
                timestamp: new Date().toISOString()
            };
            
            this.testResults.push(result);
            
            const status = passed ? '✅' : '❌';
            console.log(`  ${status} ${testName}${details ? ': ' + details : ''}`);
        }
        
        // ===== DATABASE TESTS =====
        
        async runDatabaseTests() {
            // Test 1: Database availability
            this.recordTest('Database Available', !!window.AgentLeeDB);
            
            if (!window.AgentLeeDB) return;
            
            // Test 2: Schema validation
            const expectedTables = [
                'agents', 'agent_workflows', 'tasks', 'workers', 'llm_sessions',
                'azr_nodes', 'execution_logs', 'healing_logs', 'diagnostics',
                'gui_registry', 'telemetry', 'speech_styles', 'engagement_signals',
                'user_behavior', 'memory_fragments', 'learning_models'
            ];
            
            const actualTables = Array.from(window.AgentLeeDB.objectStoreNames);
            const missingTables = expectedTables.filter(table => !actualTables.includes(table));
            
            this.recordTest('Schema Completeness', missingTables.length === 0, 
                missingTables.length > 0 ? `Missing: ${missingTables.join(', ')}` : `All ${expectedTables.length} tables found`);
            
            // Test 3: Database helpers availability
            this.recordTest('Database Helpers Available', !!window.AgentLeeDBHelpers);
            
            if (!window.AgentLeeDBHelpers) return;
            
            // Test 4: CRUD operations
            try {
                const testRecord = {
                    test_id: `test_${Date.now()}`,
                    test_data: 'automated_test',
                    timestamp: new Date().toISOString()
                };
                
                // Test add
                await window.AgentLeeDBHelpers.addRecord('telemetry', {
                    entry_id: testRecord.test_id,
                    region: 'Test Suite',
                    event_type: 'automated_test',
                    timestamp: testRecord.timestamp,
                    metadata: testRecord
                });
                
                // Test query
                const retrieved = await window.AgentLeeDBHelpers.queryByIndex('telemetry', 'event_type', 'automated_test');
                const found = retrieved.some(r => r.entry_id === testRecord.test_id);
                
                this.recordTest('CRUD Operations', found, 'Add and query operations successful');
                
            } catch (error) {
                this.recordTest('CRUD Operations', false, error.message);
            }
            
            // Test 5: Performance - large dataset query
            try {
                const start = performance.now();
                await window.AgentLeeDBHelpers.getAllRecords('telemetry');
                const duration = performance.now() - start;
                
                this.recordTest('Query Performance', duration < 1000, `Query took ${duration.toFixed(2)}ms`);
            } catch (error) {
                this.recordTest('Query Performance', false, error.message);
            }
        }
        
        // ===== EVENT BUS TESTS =====
        
        async runEventBusTests() {
            // Test 1: Event bus availability
            this.recordTest('Event Bus Available', !!window.AgentLeeEventBus);
            
            if (!window.AgentLeeEventBus) return;
            
            // Test 2: Event broadcasting and receiving
            return new Promise((resolve) => {
                let eventReceived = false;
                const testMessage = { 
                    type: 'AutomatedTestEvent', 
                    data: { test: true, timestamp: Date.now() } 
                };
                
                const listener = (event) => {
                    if (event.data.type === 'AutomatedTestEvent') {
                        eventReceived = true;
                        window.AgentLeeEventBus.removeEventListener('message', listener);
                        this.recordTest('Event Broadcasting', true, 'Event sent and received successfully');
                        resolve();
                    }
                };
                
                window.AgentLeeEventBus.addEventListener('message', listener);
                window.AgentLeeEventBus.postMessage(testMessage);
                
                // Timeout after 1 second
                setTimeout(() => {
                    if (!eventReceived) {
                        window.AgentLeeEventBus.removeEventListener('message', listener);
                        this.recordTest('Event Broadcasting', false, 'Event not received within timeout');
                        resolve();
                    }
                }, 1000);
            });
        }
        
        // ===== LLM ROUTING TESTS =====
        
        async runLLMRoutingTests() {
            // Test 1: Echo Dispatcher availability
            this.recordTest('Echo Dispatcher Available', !!window.EchoDispatcher);
            
            if (!window.EchoDispatcher) return;
            
            // Test 2: Routing logic
            const routingTests = [
                { input: 'plan a complex strategy', expected: 'azr', minConfidence: 0.7 },
                { input: 'validate email format', expected: 'phi3', minConfidence: 0.6 },
                { input: 'generate ui echo', expected: 'gemini', minConfidence: 0.6 },
                { input: 'click the button', expected: 'electron', minConfidence: 0.9 }
            ];
            
            let routingPassed = 0;
            for (const test of routingTests) {
                try {
                    const routing = window.EchoDispatcher.routeTask(test.input);
                    const correctTarget = routing.target === test.expected;
                    const sufficientConfidence = routing.confidence >= test.minConfidence;
                    const passed = correctTarget && sufficientConfidence;
                    
                    if (passed) routingPassed++;
                    
                    this.recordTest(`Routing: "${test.input}"`, passed, 
                        `${routing.target} (${routing.confidence.toFixed(2)}) expected ${test.expected}`);
                        
                } catch (error) {
                    this.recordTest(`Routing: "${test.input}"`, false, error.message);
                }
            }
            
            this.recordTest('Overall Routing Accuracy', routingPassed === routingTests.length, 
                `${routingPassed}/${routingTests.length} tests passed`);
            
            // Test 3: Confidence calculation
            try {
                const confidence = window.EchoDispatcher.calculateConfidence(
                    'plan a very complex multi-step strategy', 'plan', 'azr', {}
                );
                
                this.recordTest('Confidence Calculation', confidence > 0.7 && confidence <= 0.95, 
                    `Confidence: ${confidence.toFixed(2)}`);
            } catch (error) {
                this.recordTest('Confidence Calculation', false, error.message);
            }
        }
        
        // ===== GUI MANAGEMENT TESTS =====
        
        async runGUITests() {
            // Test 1: MicrofrontendLoader availability
            this.recordTest('MicrofrontendLoader Available', !!window.MicrofrontendLoader);
            
            if (!window.MicrofrontendLoader) return;
            
            // Test 2: GUI configurations
            const configs = window.MicrofrontendLoader.guiConfigs;
            const expectedGuis = ['agents', 'db', 'todo', 'workers', 'llmcenter', 'logs'];
            const configuredGuis = Object.keys(configs);
            const missingConfigs = expectedGuis.filter(gui => !configuredGuis.includes(gui));
            
            this.recordTest('GUI Configurations', missingConfigs.length === 0, 
                missingConfigs.length > 0 ? `Missing: ${missingConfigs.join(', ')}` : `All ${expectedGuis.length} GUIs configured`);
            
            // Test 3: Position calculation
            try {
                const position = window.MicrofrontendLoader.calculatePosition('test');
                const validPosition = position && typeof position.x === 'number' && typeof position.y === 'number';
                
                this.recordTest('Position Calculation', validPosition, 
                    validPosition ? `Position: (${position.x}, ${position.y})` : 'Invalid position returned');
            } catch (error) {
                this.recordTest('Position Calculation', false, error.message);
            }
            
            // Test 4: Loaded GUIs tracking
            try {
                const loadedGuis = window.MicrofrontendLoader.getLoadedGuis();
                this.recordTest('GUI Tracking', Array.isArray(loadedGuis), 
                    `Currently loaded: ${loadedGuis.length} GUIs`);
            } catch (error) {
                this.recordTest('GUI Tracking', false, error.message);
            }
        }
        
        // ===== PERFORMANCE TESTS =====
        
        async runPerformanceTests() {
            // Test 1: Memory usage
            if ('memory' in performance) {
                const memory = performance.memory;
                const memoryUsagePercent = (memory.usedJSHeapSize / memory.jsHeapSizeLimit) * 100;
                
                this.recordTest('Memory Usage', memoryUsagePercent < 80, 
                    `${memoryUsagePercent.toFixed(1)}% of heap limit used`);
            } else {
                this.recordTest('Memory Usage', true, 'Memory API not available');
            }
            
            // Test 2: Database query performance
            if (window.AgentLeeDBHelpers) {
                try {
                    const start = performance.now();
                    await window.AgentLeeDBHelpers.getAllRecords('telemetry');
                    const duration = performance.now() - start;
                    
                    this.recordTest('Database Performance', duration < 500, 
                        `Query took ${duration.toFixed(2)}ms`);
                } catch (error) {
                    this.recordTest('Database Performance', false, error.message);
                }
            }
            
            // Test 3: LLM routing performance
            if (window.EchoDispatcher) {
                const start = performance.now();
                for (let i = 0; i < 100; i++) {
                    window.EchoDispatcher.routeTask('test input for performance');
                }
                const duration = performance.now() - start;
                const avgDuration = duration / 100;
                
                this.recordTest('LLM Routing Performance', avgDuration < 10, 
                    `Average routing time: ${avgDuration.toFixed(2)}ms`);
            }
        }
        
        // ===== INTEGRATION TESTS =====
        
        async runIntegrationTests() {
            // Test 1: End-to-end data flow
            if (window.AgentLeeDBHelpers && window.AgentLeeEventBus) {
                try {
                    let eventReceived = false;
                    const testData = {
                        entry_id: `integration_test_${Date.now()}`,
                        region: 'Integration Test',
                        event_type: 'integration_test',
                        timestamp: new Date().toISOString()
                    };
                    
                    // Listen for database change event
                    const listener = (event) => {
                        if (event.data.type === 'DBRecordChanged' && 
                            event.data.data.record.entry_id === testData.entry_id) {
                            eventReceived = true;
                            window.AgentLeeEventBus.removeEventListener('message', listener);
                        }
                    };
                    
                    window.AgentLeeEventBus.addEventListener('message', listener);
                    
                    // Add record (should trigger event)
                    await window.AgentLeeDBHelpers.addRecord('telemetry', testData);
                    
                    // Wait for event
                    await new Promise(resolve => setTimeout(resolve, 100));
                    
                    this.recordTest('Database-Event Integration', eventReceived, 
                        eventReceived ? 'Database change triggered event' : 'Event not received');
                        
                    window.AgentLeeEventBus.removeEventListener('message', listener);
                    
                } catch (error) {
                    this.recordTest('Database-Event Integration', false, error.message);
                }
            }
            
            // Test 2: LLM-Database integration
            if (window.EchoDispatcher && window.AgentLeeDBHelpers) {
                try {
                    // This would test actual LLM execution and logging
                    // For now, just test the routing and logging capability
                    const routing = window.EchoDispatcher.routeTask('test integration');
                    
                    this.recordTest('LLM-Database Integration', !!routing.target, 
                        `Routing successful: ${routing.target}`);
                } catch (error) {
                    this.recordTest('LLM-Database Integration', false, error.message);
                }
            }
        }
        
        // ===== REPORTING =====
        
        generateTestReport() {
            const totalTests = this.testResults.length;
            const passedTests = this.testResults.filter(r => r.passed).length;
            const failedTests = totalTests - passedTests;
            const successRate = ((passedTests / totalTests) * 100).toFixed(1);
            
            const report = {
                summary: {
                    total: totalTests,
                    passed: passedTests,
                    failed: failedTests,
                    success_rate: `${successRate}%`,
                    timestamp: new Date().toISOString()
                },
                results: this.testResults,
                suites: this.groupResultsBySuite()
            };
            
            console.log('\n📊 Test Suite Complete:');
            console.log(`   Total Tests: ${totalTests}`);
            console.log(`   Passed: ${passedTests}`);
            console.log(`   Failed: ${failedTests}`);
            console.log(`   Success Rate: ${successRate}%`);
            
            return report;
        }
        
        groupResultsBySuite() {
            const suites = {};
            for (const result of this.testResults) {
                if (!suites[result.suite]) {
                    suites[result.suite] = { passed: 0, failed: 0, tests: [] };
                }
                
                if (result.passed) {
                    suites[result.suite].passed++;
                } else {
                    suites[result.suite].failed++;
                }
                
                suites[result.suite].tests.push(result);
            }
            return suites;
        }
        
        exportTestReport() {
            const report = this.generateTestReport();
            const blob = new Blob([JSON.stringify(report, null, 2)], { type: 'application/json' });
            const url = URL.createObjectURL(blob);
            
            const a = document.createElement('a');
            a.href = url;
            a.download = `agent_lee_test_report_${Date.now()}.json`;
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            URL.revokeObjectURL(url);
        }
    }

    // Initialize global test suite
    window.AgentLeeTestSuite = new AgentLeeTestSuite();
})();
// --- END OF FILE automated_test_suite.js ---

// --- START OF FILE agentStatus.js (Manager Class) ---
/**
 * Agent Lee Status Management System
 * Handles agent states, task assignments, and performance monitoring
 */
(function() {
    class AgentStatusManager {
        constructor() {
            this.agentStates = new Map();
            this.taskAssignments = new Map();
            this.performanceMetrics = new Map();
            this.statusHistory = [];
            this.updateInterval = null;
            
            // Agent types based on the file requirements
            this.agentTypes = {
                'nexus_agent': {
                    capabilities: ['coordination', 'task_delegation', 'system_oversight'],
                    maxConcurrentTasks: 5,
                    priority: 1
                },
                'llm_agent': {
                    capabilities: ['reasoning', 'language_processing', 'content_generation'],
                    maxConcurrentTasks: 3,
                    priority: 2
                },
                'worker_agent': {
                    capabilities: ['task_execution', 'data_processing', 'system_operations'],
                    maxConcurrentTasks: 10,
                    priority: 3
                },
                'specialist_agent': {
                    capabilities: ['domain_expertise', 'specialized_processing'],
                    maxConcurrentTasks: 2,
                    priority: 2
                }
            };
            
            console.log('👤 Agent Status Manager initialized');
            this.startStatusMonitoring();
        }
        
        // ===== AGENT REGISTRATION =====
        
        async registerAgent(agentId, agentConfig) {
            const agent = {
                id: agentId,
                name: agentConfig.name || agentId,
                type: agentConfig.type || 'worker_agent',
                status: 'idle',
                capabilities: agentConfig.capabilities || this.agentTypes[agentConfig.type]?.capabilities || [],
                maxConcurrentTasks: agentConfig.maxConcurrentTasks || this.agentTypes[agentConfig.type]?.maxConcurrentTasks || 3,
                currentTasks: [],
                performance: {
                    tasksCompleted: 0,
                    tasksSuccessful: 0,
                    averageExecutionTime: 0,
                    lastTaskTime: null,
                    efficiency: 1.0
                },
                metadata: agentConfig.metadata || {},
                registeredAt: new Date().toISOString(),
                lastHeartbeat: new Date().toISOString()
            };
            
            this.agentStates.set(agentId, agent);
            this.performanceMetrics.set(agentId, {
                executionTimes: [],
                successRate: 1.0,
                loadFactor: 0.0,
                responseTime: 0
            });
            
            // Store in database
            if (window.AgentLeeDBHelpers) {
                try {
                    await window.AgentLeeDBHelpers.addRecord('agents', {
                        agent_id: agentId,
                        name: agent.name,
                        type: agent.type,
                        status: agent.status,
                        capabilities: agent.capabilities,
                        performance_score: agent.performance.efficiency,
                        metadata: agent.metadata,
                        created_at: agent.registeredAt,
                        last_heartbeat: agent.lastHeartbeat
                    });
                } catch (error) {
                    console.error('Failed to store agent in database:', error);
                }
            }
            
            console.log(`👤 Agent registered: ${agentId} (${agent.type})`);
            this.broadcastStatusUpdate(agentId, 'registered');
            
            return agent;
        }
        
        // ===== STATUS MANAGEMENT =====
        
        async updateAgentStatus(agentId, newStatus, metadata = {}) {
            const agent = this.agentStates.get(agentId);
            if (!agent) {
                throw new Error(`Agent not found: ${agentId}`);
            }
            
            const previousStatus = agent.status;
            agent.status = newStatus;
            agent.lastHeartbeat = new Date().toISOString();
            
            // Update metadata if provided
            if (Object.keys(metadata).length > 0) {
                agent.metadata = { ...agent.metadata, ...metadata };
            }
            
            // Record status change
            this.statusHistory.push({
                agentId,
                previousStatus,
                newStatus,
                timestamp: new Date().toISOString(),
                metadata
            });
            
            // Update database
            if (window.AgentLeeDBHelpers) {
                try {
                    await window.AgentLeeDBHelpers.updateRecord('agents', agentId, {
                        status: newStatus,
                        metadata: agent.metadata,
                        last_heartbeat: agent.lastHeartbeat
                    });
                } catch (error) {
                    console.error('Failed to update agent status in database:', error);
                }
            }
            
            console.log(`👤 Agent ${agentId} status: ${previousStatus} → ${newStatus}`);
            this.broadcastStatusUpdate(agentId, newStatus, { previousStatus, metadata });
            
            return agent;
        }
        
        getAgentStatus(agentId) {
            const agent = this.agentStates.get(agentId);
            if (!agent) {
                return null;
            }
            
            const metrics = this.performanceMetrics.get(agentId);
            return {
                ...agent,
                metrics: metrics || {},
                currentLoad: agent.currentTasks.length / agent.maxConcurrentTasks
            };
        }
        
        getAllAgentStatuses() {
            const statuses = {};
            for (const [agentId, agent] of this.agentStates) {
                statuses[agentId] = this.getAgentStatus(agentId);
            }
            return statuses;
        }
        
        // ===== TASK ASSIGNMENT =====
        
        async assignTask(agentId, taskData) {
            const agent = this.agentStates.get(agentId);
            if (!agent) {
                throw new Error(`Agent not found: ${agentId}`);
            }
            
            if (agent.currentTasks.length >= agent.maxConcurrentTasks) {
                throw new Error(`Agent ${agentId} is at maximum capacity`);
            }
            
            const task = {
                id: taskData.id || `task_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
                type: taskData.type,
                description: taskData.description,
                priority: taskData.priority || 0.5,
                assignedAt: new Date().toISOString(),
                startedAt: null,
                completedAt: null,
                status: 'assigned',
                metadata: taskData.metadata || {}
            };
            
            agent.currentTasks.push(task);
            this.taskAssignments.set(task.id, {
                agentId,
                task,
                assignedAt: task.assignedAt
            });
            
            // Update agent status if idle
            if (agent.status === 'idle') {
                await this.updateAgentStatus(agentId, 'busy', { reason: 'task_assigned' });
            }
            
            // Store task in database
            if (window.AgentLeeDBHelpers) {
                try {
                    await window.AgentLeeDBHelpers.addRecord('tasks', {
                        task_id: task.id,
                        assigned_to: agentId,
                        task_type: task.type,
                        description: task.description,
                        priority: task.priority,
                        status: task.status,
                        created_at: task.assignedAt,
                        metadata: task.metadata
                    });
                } catch (error) {
                    console.error('Failed to store task in database:', error);
                }
            }
            
            console.log(`📋 Task assigned: ${task.id} → ${agentId}`);
            this.broadcastTaskUpdate(task.id, 'assigned', { agentId });
            
            return task;
        }
        
        async updateTaskStatus(taskId, newStatus, metadata = {}) {
            const assignment = this.taskAssignments.get(taskId);
            if (!assignment) {
                throw new Error(`Task not found: ${taskId}`);
            }
            
            const { agentId, task } = assignment;
            const agent = this.agentStates.get(agentId);
            
            const previousStatus = task.status;
            task.status = newStatus;
            
            // Update timestamps
            if (newStatus === 'in_progress' && !task.startedAt) {
                task.startedAt = new Date().toISOString();
            } else if (['completed', 'failed', 'cancelled'].includes(newStatus) && !task.completedAt) {
                task.completedAt = new Date().toISOString();
                
                // Remove from agent's current tasks
                const taskIndex = agent.currentTasks.findIndex(t => t.id === taskId);
                if (taskIndex !== -1) {
                    agent.currentTasks.splice(taskIndex, 1);
                }
                
                // Update performance metrics
                await this.updatePerformanceMetrics(agentId, task, newStatus === 'completed');
                
                // Update agent status if no more tasks
                if (agent.currentTasks.length === 0) {
                    await this.updateAgentStatus(agentId, 'idle', { reason: 'all_tasks_completed' });
                }
            }
            
            // Update metadata
            task.metadata = { ...task.metadata, ...metadata };
            
            // Update database
            if (window.AgentLeeDBHelpers) {
                try {
                    await window.AgentLeeDBHelpers.updateRecord('tasks', taskId, {
                        status: newStatus,
                        started_at: task.startedAt,
                        completed_at: task.completedAt,
                        metadata: task.metadata
                    });
                } catch (error) {
                    console.error('Failed to update task in database:', error);
                }
            }
            
            console.log(`📋 Task ${taskId} status: ${previousStatus} → ${newStatus}`);
            this.broadcastTaskUpdate(taskId, newStatus, { agentId, previousStatus, metadata });
            
            return task;
        }
        
        // ===== PERFORMANCE MONITORING =====
        
        async updatePerformanceMetrics(agentId, task, successful) {
            const agent = this.agentStates.get(agentId);
            const metrics = this.performanceMetrics.get(agentId);
            
            if (!agent || !metrics) return;
            
            // Calculate execution time
            let executionTime = 0;
            if (task.startedAt && task.completedAt) {
                executionTime = new Date(task.completedAt).getTime() - new Date(task.startedAt).getTime();
                metrics.executionTimes.push(executionTime);
                
                // Keep only last 100 execution times
                if (metrics.executionTimes.length > 100) {
                    metrics.executionTimes.shift();
                }
            }
            
            // Update performance counters
            agent.performance.tasksCompleted++;
            if (successful) {
                agent.performance.tasksSuccessful++;
            }
            
            // Calculate metrics
            agent.performance.averageExecutionTime = metrics.executionTimes.reduce((sum, time) => sum + time, 0) / metrics.executionTimes.length || 0;
            agent.performance.lastTaskTime = task.completedAt;
            
            metrics.successRate = agent.performance.tasksSuccessful / agent.performance.tasksCompleted;
            metrics.loadFactor = agent.currentTasks.length / agent.maxConcurrentTasks;
            metrics.responseTime = executionTime;
            
            // Calculate efficiency score
            agent.performance.efficiency = this.calculateEfficiencyScore(metrics);
            
            // Update database
            if (window.AgentLeeDBHelpers) {
                try {
                    await window.AgentLeeDBHelpers.updateRecord('agents', agentId, {
                        performance_score: agent.performance.efficiency,
                        last_task_completion: task.completedAt
                    });
                } catch (error) {
                    console.error('Failed to update agent performance in database:', error);
                }
            }
            
            console.log(`📊 Performance updated for ${agentId}: efficiency=${agent.performance.efficiency.toFixed(2)}`);
        }
        
        calculateEfficiencyScore(metrics) {
            // Weighted efficiency calculation
            const successWeight = 0.4;
            const speedWeight = 0.3;
            const loadWeight = 0.3;
            
            const successScore = metrics.successRate;
            const speedScore = Math.max(0, 1 - (metrics.responseTime / 30000)); // Normalize to 30 seconds
            const loadScore = 1 - metrics.loadFactor; // Lower load is better for availability
            
            return (successScore * successWeight) + (speedScore * speedWeight) + (loadScore * loadWeight);
        }
        
        // ===== MONITORING & MAINTENANCE =====
        
        startStatusMonitoring() {
            this.updateInterval = setInterval(() => {
                this.performHealthCheck();
            }, 30000); // Every 30 seconds
        }
        
        async performHealthCheck() {
            const now = Date.now();
            const staleThreshold = 5 * 60 * 1000; // 5 minutes
            
            for (const [agentId, agent] of this.agentStates) {
                const lastHeartbeat = new Date(agent.lastHeartbeat).getTime();
                
                if (now - lastHeartbeat > staleThreshold) {
                    console.warn(`⚠️ Agent ${agentId} appears stale (last heartbeat: ${agent.lastHeartbeat})`);
                    
                    if (agent.status !== 'offline') {
                        await this.updateAgentStatus(agentId, 'offline', { 
                            reason: 'heartbeat_timeout',
                            lastSeen: agent.lastHeartbeat
                        });
                    }
                }
            }
        }
        
        async heartbeat(agentId, metadata = {}) {
            const agent = this.agentStates.get(agentId);
            if (!agent) {
                throw new Error(`Agent not found: ${agentId}`);
            }
            
            agent.lastHeartbeat = new Date().toISOString();
            
            // If agent was offline, bring it back online
            if (agent.status === 'offline') {
                await this.updateAgentStatus(agentId, 'idle', { 
                    reason: 'heartbeat_restored',
                    ...metadata
                });
            }
            
            return { success: true, timestamp: agent.lastHeartbeat };
        }
        
        // ===== EVENT BROADCASTING =====
        
        broadcastStatusUpdate(agentId, status, metadata = {}) {
            if (window.AgentLeeEventBus) {
                window.AgentLeeEventBus.postMessage({
                    type: 'AgentStatusUpdate',
                    data: {
                        agentId,
                        status,
                        timestamp: new Date().toISOString(),
                        ...metadata
                    }
                });
            }
        }
        
        broadcastTaskUpdate(taskId, status, metadata = {}) {
            if (window.AgentLeeEventBus) {
                window.AgentLeeEventBus.postMessage({
                    type: 'TaskStatusUpdate',
                    data: {
                        taskId,
                        status,
                        timestamp: new Date().toISOString(),
                        ...metadata
                    }
                });
            }
        }
        
        // ===== PUBLIC API =====
        
        getSystemOverview() {
            const agents = Array.from(this.agentStates.values());
            const tasks = Array.from(this.taskAssignments.values());
            
            return {
                totalAgents: agents.length,
                activeAgents: agents.filter(a => a.status !== 'offline').length,
                busyAgents: agents.filter(a => a.status === 'busy').length,
                totalTasks: tasks.length,
                activeTasks: tasks.filter(t => ['assigned', 'in_progress'].includes(t.task.status)).length,
                completedTasks: tasks.filter(t => t.task.status === 'completed').length,
                averageEfficiency: agents.reduce((sum, a) => sum + a.performance.efficiency, 0) / agents.length || 0,
                systemLoad: agents.reduce((sum, a) => sum + (a.currentTasks.length / a.maxConcurrentTasks), 0) / agents.length || 0
            };
        }
        
        async exportAgentData() {
            return {
                agents: Array.from(this.agentStates.entries()),
                tasks: Array.from(this.taskAssignments.entries()),
                performance: Array.from(this.performanceMetrics.entries()),
                statusHistory: this.statusHistory,
                systemOverview: this.getSystemOverview(),
                timestamp: new Date().toISOString()
            };
        }
    }

    // Initialize global Agent Status Manager
    window.AgentStatusManager = new AgentStatusManager();
})();
// --- END OF FILE agentStatus.js (Manager Class) ---

// --- START OF FILE cameraDiagnostics.js (Manager Class) ---
/**
 * Agent Lee Camera Diagnostics System
 * Handles camera access, diagnostics, and visual processing capabilities
 */
(function() {
    class CameraDiagnosticsSystem {
        constructor() {
            this.cameras = new Map();
            this.activeStreams = new Map();
            this.diagnosticResults = [];
            this.isInitialized = false;
            this.capabilities = {
                getUserMedia: 'mediaDevices' in navigator && 'getUserMedia' in navigator.mediaDevices,
                enumerateDevices: 'mediaDevices' in navigator && 'enumerateDevices' in navigator.mediaDevices,
                getDisplayMedia: 'mediaDevices' in navigator && 'getDisplayMedia' in navigator.mediaDevices
            };
            
            console.log('📷 Camera Diagnostics System initialized');
            this.initializeCameraSystem();
        }
        
        // ===== INITIALIZATION =====
        
        async initializeCameraSystem() {
            try {
                if (!this.capabilities.getUserMedia) {
                    console.warn('📷 Camera access not supported in this browser');
                    return;
                }
                
                // Enumerate available cameras
                await this.enumerateCameras();
                
                // Run initial diagnostics
                await this.runCameraDiagnostics();
                
                this.isInitialized = true;
                console.log('📷 Camera system initialized successfully');
                
            } catch (error) {
                console.error('❌ Failed to initialize camera system:', error);
            }
        }
        
        async enumerateCameras() {
            if (!this.capabilities.enumerateDevices) {
                console.warn('📷 Device enumeration not supported');
                return;
            }
            
            try {
                const devices = await navigator.mediaDevices.enumerateDevices();
                const videoDevices = devices.filter(device => device.kind === 'videoinput');
                
                this.cameras.clear();
                
                for (const device of videoDevices) {
                    this.cameras.set(device.deviceId, {
                        id: device.deviceId,
                        label: device.label || `Camera ${this.cameras.size + 1}`,
                        groupId: device.groupId,
                        kind: device.kind,
                        status: 'available',
                        capabilities: null,
                        lastTested: null
                    });
                }
                
                console.log(`📷 Found ${this.cameras.size} camera(s)`);
                
            } catch (error) {
                console.error('❌ Failed to enumerate cameras:', error);
            }
        }
        
        // ===== CAMERA DIAGNOSTICS =====
        
        async runCameraDiagnostics() {
            console.log('📷 Running camera diagnostics...');
            
            const diagnostics = {
                timestamp: new Date().toISOString(),
                browser_support: this.capabilities,
                cameras_found: this.cameras.size,
                camera_tests: [],
                permissions: await this.checkCameraPermissions(),
                overall_status: 'unknown'
            };
            
            // Test each camera
            for (const [deviceId, camera] of this.cameras) {
                const cameraTest = await this.testCamera(deviceId);
                diagnostics.camera_tests.push(cameraTest);
            }
            
            // Determine overall status
            const workingCameras = diagnostics.camera_tests.filter(test => test.status === 'working').length;
            if (workingCameras === 0) {
                diagnostics.overall_status = 'no_cameras_working';
            } else if (workingCameras === this.cameras.size) {
                diagnostics.overall_status = 'all_cameras_working';
            } else {
                diagnostics.overall_status = 'some_cameras_working';
            }
            
            this.diagnosticResults.push(diagnostics);
            
            // Store in database
            if (window.AgentLeeDBHelpers) {
                try {
                    await window.AgentLeeDBHelpers.addRecord('diagnostics', {
                        diagnostic_id: `camera_diag_${Date.now()}`,
                        module_name: 'camera_system',
                        health_score: (workingCameras / Math.max(this.cameras.size, 1)) * 100,
                        status: diagnostics.overall_status,
                        timestamp: diagnostics.timestamp,
                        metadata: diagnostics
                    });
                } catch (error) {
                    console.error('Failed to store camera diagnostics:', error);
                }
            }
            
            console.log(`📷 Camera diagnostics complete: ${diagnostics.overall_status}`);
            return diagnostics;
        }
        
        async testCamera(deviceId) {
            const camera = this.cameras.get(deviceId);
            if (!camera) {
                return { deviceId, status: 'not_found', error: 'Camera not found' };
            }
            
            const test = {
                deviceId,
                label: camera.label,
                status: 'testing',
                error: null,
                capabilities: null,
                resolution_test: null,
                frame_rate_test: null,
                test_duration: 0
            };
            
            const startTime = performance.now();
            
            try {
                // Request camera access
                const stream = await navigator.mediaDevices.getUserMedia({
                    video: { deviceId: { exact: deviceId } }
                });
                
                // Get camera capabilities
                const track = stream.getVideoTracks()[0];
                test.capabilities = track.getCapabilities ? track.getCapabilities() : null;
                
                // Test resolution
                test.resolution_test = await this.testCameraResolution(stream);
                
                // Test frame rate
                test.frame_rate_test = await this.testCameraFrameRate(stream);
                
                // Clean up
                stream.getTracks().forEach(track => track.stop());
                
                test.status = 'working';
                camera.status = 'working';
                camera.lastTested = new Date().toISOString();
                
            } catch (error) {
                test.status = 'error';
                test.error = error.message;
                camera.status = 'error';
                console.error(`❌ Camera test failed for ${camera.label}:`, error);
            }
            
            test.test_duration = performance.now() - startTime;
            return test;
        }
        
        async testCameraResolution(stream) {
            return new Promise((resolve) => {
                const video = document.createElement('video');
                video.srcObject = stream;
                video.play();
                
                video.onloadedmetadata = () => {
                    const resolution = {
                        width: video.videoWidth,
                        height: video.videoHeight,
                        aspect_ratio: (video.videoWidth / video.videoHeight).toFixed(2)
                    };
                    resolve(resolution);
                };
                
                // Timeout after 5 seconds
                setTimeout(() => {
                    resolve({ error: 'Resolution test timeout' });
                }, 5000);
            });
        }
        
        async testCameraFrameRate(stream) {
            return new Promise((resolve) => {
                const video = document.createElement('video');
                video.srcObject = stream;
                video.play();
                
                let frameCount = 0;
                const startTime = performance.now();
                
                const countFrames = () => {
                    frameCount++;
                    if (performance.now() - startTime < 2000) { // Test for 2 seconds
                        requestAnimationFrame(countFrames);
                    } else {
                        const fps = frameCount / 2; // Frames per second
                        resolve({ fps: Math.round(fps), frame_count: frameCount });
                    }
                };
                
                video.onplaying = () => {
                    requestAnimationFrame(countFrames);
                };
                
                // Timeout after 5 seconds
                setTimeout(() => {
                    resolve({ error: 'Frame rate test timeout' });
                }, 5000);
            });
        }
        
        async checkCameraPermissions() {
            if (!('permissions' in navigator)) {
                return { status: 'unknown', message: 'Permissions API not supported' };
            }
            
            try {
                const permission = await navigator.permissions.query({ name: 'camera' });
                return {
                    status: permission.state,
                    message: `Camera permission: ${permission.state}`
                };
            } catch (error) {
                return {
                    status: 'error',
                    message: `Permission check failed: ${error.message}`
                };
            }
        }
        
        // ===== CAMERA OPERATIONS =====
        
        async startCamera(deviceId, constraints = {}) {
            if (this.activeStreams.has(deviceId)) {
                console.warn(`📷 Camera ${deviceId} is already active`);
                return this.activeStreams.get(deviceId);
            }
            
            try {
                const defaultConstraints = {
                    video: {
                        deviceId: deviceId ? { exact: deviceId } : undefined,
                        width: { ideal: 1280 },
                        height: { ideal: 720 },
                        frameRate: { ideal: 30 }
                    }
                };
                
                const finalConstraints = { ...defaultConstraints, ...constraints };
                const stream = await navigator.mediaDevices.getUserMedia(finalConstraints);
                
                this.activeStreams.set(deviceId || 'default', {
                    stream,
                    deviceId,
                    startTime: new Date().toISOString(),
                    constraints: finalConstraints
                });
                
                console.log(`📷 Camera started: ${deviceId || 'default'}`);
                return stream;
                
            } catch (error) {
                console.error(`❌ Failed to start camera ${deviceId}:`, error);
                throw error;
            }
        }
        
        stopCamera(deviceId) {
            const streamInfo = this.activeStreams.get(deviceId);
            if (!streamInfo) {
                console.warn(`📷 Camera ${deviceId} is not active`);
                return;
            }
            
            streamInfo.stream.getTracks().forEach(track => track.stop());
            this.activeStreams.delete(deviceId);
            
            console.log(`📷 Camera stopped: ${deviceId}`);
        }
        
        stopAllCameras() {
            for (const [deviceId] of this.activeStreams) {
                this.stopCamera(deviceId);
            }
            console.log('📷 All cameras stopped');
        }
        
        // ===== SCREEN CAPTURE =====
        
        async startScreenCapture(constraints = {}) {
            if (!this.capabilities.getDisplayMedia) {
                throw new Error('Screen capture not supported in this browser');
            }
            
            try {
                const defaultConstraints = {
                    video: {
                        width: { ideal: 1920 },
                        height: { ideal: 1080 },
                        frameRate: { ideal: 30 }
                    },
                    audio: false
                };
                
                const finalConstraints = { ...defaultConstraints, ...constraints };
                const stream = await navigator.mediaDevices.getDisplayMedia(finalConstraints);
                
                this.activeStreams.set('screen', {
                    stream,
                    deviceId: 'screen',
                    startTime: new Date().toISOString(),
                    constraints: finalConstraints
                });
                
                console.log('📷 Screen capture started');
                return stream;
                
            } catch (error) {
                console.error('❌ Failed to start screen capture:', error);
                throw error;
            }
        }
        
        // ===== PUBLIC API =====
        
        getCameraList() {
            return Array.from(this.cameras.values());
        }
        
        getActiveStreams() {
            return Array.from(this.activeStreams.entries()).map(([id, info]) => ({
                id,
                deviceId: info.deviceId,
                startTime: info.startTime,
                isActive: info.stream.active
            }));
        }
        
        getDiagnosticResults() {
            return this.diagnosticResults;
        }
        
        getSystemCapabilities() {
            return {
                ...this.capabilities,
                cameras_available: this.cameras.size,
                active_streams: this.activeStreams.size,
                is_initialized: this.isInitialized
            };
        }
        
        async refreshCameraList() {
            await this.enumerateCameras();
            return this.getCameraList();
        }
        
        async runFullDiagnostics() {
            await this.enumerateCameras();
            return await this.runCameraDiagnostics();
        }
        
        async exportDiagnosticData() {
            return {
                cameras: Array.from(this.cameras.values()),
                active_streams: this.getActiveStreams(),
                diagnostic_results: this.diagnosticResults,
                capabilities: this.getSystemCapabilities(),
                timestamp: new Date().toISOString()
            };
        }
    }

    // Initialize global Camera Diagnostics System
    window.CameraDiagnosticsSystem = new CameraDiagnosticsSystem();
})();
// --- END OF FILE cameraDiagnostics.js (Manager Class) ---

// --- START OF FILE agentMemory.js (Manager Class) ---
/**
 * Agent Lee Memory Management System
 * Handles episodic, semantic, and working memory
 * Integrates with database and cognitive matrix
 */
(function() {
    class AgentMemorySystem {
        constructor() {
            this.workingMemory = new Map();
            this.episodicMemory = new Map();
            this.semanticMemory = new Map();
            this.memoryCache = new Map();
            this.memoryMetrics = {
                totalMemories: 0,
                accessCount: 0,
                lastConsolidation: null,
                averageRetrieval: 0
            };
            
            // Memory configuration based on file requirements
            this.memoryConfig = {
                workingMemoryLimit: 7, // Miller's magic number
                episodicRetentionDays: 30,
                semanticStrengthThreshold: 0.7,
                consolidationInterval: 300000, // 5 minutes
                cacheSize: 100
            };
            
            console.log('🧠 Agent Memory System initialized');
            this.startMemoryMaintenance();
        }
        
        // ===== WORKING MEMORY =====
        
        addToWorkingMemory(key, data, priority = 0.5) {
            const memoryItem = {
                key,
                data,
                priority,
                timestamp: Date.now(),
                accessCount: 0,
                lastAccessed: Date.now()
            };
            
            this.workingMemory.set(key, memoryItem);
            
            // Maintain working memory limit
            if (this.workingMemory.size > this.memoryConfig.workingMemoryLimit) {
                this.evictFromWorkingMemory();
            }
            
            console.log(`🧠 Added to working memory: ${key}`);
            return memoryItem;
        }
        
        getFromWorkingMemory(key) {
            const item = this.workingMemory.get(key);
            if (item) {
                item.accessCount++;
                item.lastAccessed = Date.now();
                this.memoryMetrics.accessCount++;
                console.log(`🧠 Retrieved from working memory: ${key}`);
            }
            return item?.data || null;
        }
        
        evictFromWorkingMemory() {
            // Remove least recently used item with lowest priority
            let lruItem = null;
            let lruKey = null;
            let oldestTime = Date.now();
            
            for (const [key, item] of this.workingMemory) {
                const score = item.lastAccessed - (item.priority * 10000);
                if (score < oldestTime) {
                    oldestTime = score;
                    lruItem = item;
                    lruKey = key;
                }
            }
            
            if (lruKey) {
                // Move to episodic memory before eviction
                this.addToEpisodicMemory(lruKey, lruItem.data, {
                    originalPriority: lruItem.priority,
                    accessCount: lruItem.accessCount,
                    workingMemoryDuration: Date.now() - lruItem.timestamp
                });
                
                this.workingMemory.delete(lruKey);
                console.log(`🧠 Evicted from working memory: ${lruKey}`);
            }
        }
        
        // ===== EPISODIC MEMORY =====
        
        async addToEpisodicMemory(key, data, metadata = {}) {
            const episodeId = `episode_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
            
            const episode = {
                id: episodeId,
                key,
                data,
                metadata,
                timestamp: new Date().toISOString(),
                accessCount: 0,
                lastAccessed: null,
                emotionalWeight: metadata.emotionalWeight || 0.5,
                contextTags: metadata.contextTags || []
            };
            
            this.episodicMemory.set(episodeId, episode);
            
            // Store in database
            if (window.AgentLeeDBHelpers) {
                try {
                    await window.AgentLeeDBHelpers.addRecord('memory_fragments', {
                        fragment_id: episodeId,
                        memory_type: 'episodic',
                        content: JSON.stringify({ key, data, metadata }),
                        relevance_score: metadata.relevance || 0.5,
                        emotional_weight: episode.emotionalWeight,
                        last_accessed: episode.timestamp,
                        metadata: {
                            contextTags: episode.contextTags,
                            originalKey: key
                        }
                    });
                } catch (error) {
                    console.error('Failed to store episodic memory:', error);
                }
            }
            
            this.memoryMetrics.totalMemories++;
            console.log(`🧠 Added to episodic memory: ${episodeId}`);
            return episode;
        }
        
        async searchEpisodicMemory(query, limit = 10) {
            const results = [];
            const queryLower = query.toLowerCase();
            
            // Search in-memory episodes
            for (const [id, episode] of this.episodicMemory) {
                const searchText = `${episode.key} ${JSON.stringify(episode.data)}`.toLowerCase();
                if (searchText.includes(queryLower)) {
                    episode.accessCount++;
                    episode.lastAccessed = new Date().toISOString();
                    results.push(episode);
                }
            }
            
            // Search database episodes
            if (window.AgentLeeDBHelpers) {
                try {
                    const dbMemories = await window.AgentLeeDBHelpers.queryByIndex('memory_fragments', 'memory_type', 'episodic');
                    for (const memory of dbMemories) {
                        if (memory.content && memory.content.toLowerCase().includes(queryLower)) {
                            results.push({
                                id: memory.fragment_id,
                                data: JSON.parse(memory.content),
                                timestamp: memory.last_accessed,
                                relevance: memory.relevance_score,
                                emotionalWeight: memory.emotional_weight
                            });
                        }
                    }
                } catch (error) {
                    console.error('Failed to search database episodes:', error);
                }
            }
            
            // Sort by relevance and recency
            results.sort((a, b) => {
                const aScore = (a.relevance || 0.5) + (a.emotionalWeight || 0.5);
                const bScore = (b.relevance || 0.5) + (b.emotionalWeight || 0.5);
                return bScore - aScore;
            });
            
            console.log(`🧠 Found ${results.length} episodic memories for: ${query}`);
            return results.slice(0, limit);
        }
        
        // ===== SEMANTIC MEMORY =====
        
        async addToSemanticMemory(concept, knowledge, confidence = 0.8) {
            const conceptKey = concept.toLowerCase().replace(/\s+/g, '_');
            
            let semanticEntry = this.semanticMemory.get(conceptKey);
            if (!semanticEntry) {
                semanticEntry = {
                    concept,
                    knowledge: [],
                    totalConfidence: 0,
                    lastUpdated: new Date().toISOString(),
                    accessCount: 0,
                    connections: new Set()
                };
                this.semanticMemory.set(conceptKey, semanticEntry);
            }
            
            // Add new knowledge
            semanticEntry.knowledge.push({
                content: knowledge,
                confidence,
                timestamp: new Date().toISOString(),
                source: 'user_interaction'
            });
            
            // Update confidence
            semanticEntry.totalConfidence = semanticEntry.knowledge.reduce((sum, k) => sum + k.confidence, 0) / semanticEntry.knowledge.length;
            semanticEntry.lastUpdated = new Date().toISOString();
            
            // Store in database if confidence is high enough
            if (semanticEntry.totalConfidence >= this.memoryConfig.semanticStrengthThreshold) {
                if (window.AgentLeeDBHelpers) {
                    try {
                        await window.AgentLeeDBHelpers.addRecord('knowledge_updates', {
                            update_id: `semantic_${Date.now()}`,
                            update_type: 'semantic_knowledge',
                            source: 'memory_system',
                            confidence: semanticEntry.totalConfidence,
                            timestamp: semanticEntry.lastUpdated,
                            content: JSON.stringify({
                                concept: semanticEntry.concept,
                                knowledge: semanticEntry.knowledge
                            })
                        });
                    } catch (error) {
                        console.error('Failed to store semantic memory:', error);
                    }
                }
            }
            
            console.log(`🧠 Added to semantic memory: ${concept} (confidence: ${confidence})`);
            return semanticEntry;
        }
        
        getSemanticKnowledge(concept) {
            const conceptKey = concept.toLowerCase().replace(/\s+/g, '_');
            const entry = this.semanticMemory.get(conceptKey);
            
            if (entry) {
                entry.accessCount++;
                this.memoryMetrics.accessCount++;
                
                // Return knowledge sorted by confidence
                const sortedKnowledge = entry.knowledge.sort((a, b) => b.confidence - a.confidence);
                console.log(`🧠 Retrieved semantic knowledge: ${concept}`);
                return {
                    concept: entry.concept,
                    knowledge: sortedKnowledge,
                    confidence: entry.totalConfidence,
                    lastUpdated: entry.lastUpdated
                };
            }
            
            return null;
        }
        
        // ===== MEMORY CONSOLIDATION =====
        
        async consolidateMemories() {
            console.log('🧠 Starting memory consolidation...');
            
            const consolidationStart = Date.now();
            let consolidatedCount = 0;
            
            // Consolidate working memory to episodic
            for (const [key, item] of this.workingMemory) {
                if (item.accessCount > 2 || item.priority > 0.7) {
                    await this.addToEpisodicMemory(key, item.data, {
                        consolidatedFrom: 'working_memory',
                        accessCount: item.accessCount,
                        priority: item.priority
                    });
                    consolidatedCount++;
                }
            }
            
            // Consolidate episodic to semantic
            for (const [id, episode] of this.episodicMemory) {
                if (episode.accessCount > 5 && episode.emotionalWeight > 0.6) {
                    // Extract concepts from episode
                    const concepts = this.extractConcepts(episode.data);
                    for (const concept of concepts) {
                        await this.addToSemanticMemory(concept, episode.data, episode.emotionalWeight);
                    }
                    consolidatedCount++;
                }
            }
            
            // Clean up old episodic memories
            await this.cleanupOldMemories();
            
            const consolidationTime = Date.now() - consolidationStart;
            this.memoryMetrics.lastConsolidation = new Date().toISOString();
            
            console.log(`🧠 Memory consolidation complete: ${consolidatedCount} memories processed in ${consolidationTime}ms`);
            
            // Broadcast consolidation event
            if (window.AgentLeeEventBus) {
                window.AgentLeeEventBus.postMessage({
                    type: 'MemoryConsolidated',
                    data: {
                        consolidatedCount,
                        consolidationTime,
                        timestamp: this.memoryMetrics.lastConsolidation
                    }
                });
            }
        }
        
        extractConcepts(data) {
            const concepts = [];
            const text = typeof data === 'string' ? data : JSON.stringify(data);
            
            // Simple concept extraction (could be enhanced with NLP)
            const words = text.toLowerCase().match(/\b\w{4,}\b/g) || [];
            const uniqueWords = [...new Set(words)];
            
            // Filter for meaningful concepts
            const stopWords = new Set(['this', 'that', 'with', 'have', 'will', 'been', 'from', 'they', 'know', 'want', 'been', 'good', 'much', 'some', 'time', 'very', 'when', 'come', 'here', 'just', 'like', 'long', 'make', 'many', 'over', 'such', 'take', 'than', 'them', 'well', 'were']);
            
            for (const word of uniqueWords) {
                if (!stopWords.has(word) && word.length > 4) {
                    concepts.push(word);
                }
            }
            
            return concepts.slice(0, 5); // Limit to top 5 concepts
        }
        
        async cleanupOldMemories() {
            const cutoffDate = Date.now() - (this.memoryConfig.episodicRetentionDays * 24 * 60 * 60 * 1000);
            let cleanedCount = 0;
            
            for (const [id, episode] of this.episodicMemory) {
                const episodeTime = new Date(episode.timestamp).getTime();
                if (episodeTime < cutoffDate && episode.accessCount < 2) {
                    this.episodicMemory.delete(id);
                    cleanedCount++;
                }
            }
            
            console.log(`🧠 Cleaned up ${cleanedCount} old memories`);
        }
        
        // ===== MEMORY MAINTENANCE =====
        
        startMemoryMaintenance() {
            // Run consolidation periodically
            setInterval(() => {
                this.consolidateMemories();
            }, this.memoryConfig.consolidationInterval);
            
            // Update cache periodically
            setInterval(() => {
                this.updateMemoryCache();
            }, 60000); // Every minute
        }
        
        updateMemoryCache() {
            // Cache frequently accessed memories
            const frequentMemories = [];
            
            for (const [key, item] of this.workingMemory) {
                if (item.accessCount > 3) {
                    frequentMemories.push({ key, data: item.data, type: 'working' });
                }
            }
            
            for (const [id, episode] of this.episodicMemory) {
                if (episode.accessCount > 5) {
                    frequentMemories.push({ key: id, data: episode, type: 'episodic' });
                }
            }
            
            // Update cache
            this.memoryCache.clear();
            for (const memory of frequentMemories.slice(0, this.memoryConfig.cacheSize)) {
                this.memoryCache.set(memory.key, memory);
            }
            
            console.log(`🧠 Memory cache updated: ${this.memoryCache.size} items cached`);
        }
        
        // ===== PUBLIC API =====
        
        async remember(key, data, type = 'working', metadata = {}) {
            switch (type) {
                case 'working':
                    return this.addToWorkingMemory(key, data, metadata.priority);
                case 'episodic':
                    return await this.addToEpisodicMemory(key, data, metadata);
                case 'semantic':
                    return await this.addToSemanticMemory(key, data, metadata.confidence);
                default:
                    throw new Error(`Unknown memory type: ${type}`);
            }
        }
        
        async recall(query, type = 'all', limit = 10) {
            const results = [];
            
            if (type === 'all' || type === 'working') {
                const workingResult = this.getFromWorkingMemory(query);
                if (workingResult) {
                    results.push({ type: 'working', data: workingResult });
                }
            }
            
            if (type === 'all' || type === 'episodic') {
                const episodicResults = await this.searchEpisodicMemory(query, limit);
                results.push(...episodicResults.map(r => ({ type: 'episodic', data: r })));
            }
            
            if (type === 'all' || type === 'semantic') {
                const semanticResult = this.getSemanticKnowledge(query);
                if (semanticResult) {
                    results.push({ type: 'semantic', data: semanticResult });
                }
            }
            
            return results.slice(0, limit);
        }
        
        getMemoryMetrics() {
            return {
                ...this.memoryMetrics,
                workingMemorySize: this.workingMemory.size,
                episodicMemorySize: this.episodicMemory.size,
                semanticMemorySize: this.semanticMemory.size,
                cacheSize: this.memoryCache.size,
                memoryUtilization: this.workingMemory.size / this.memoryConfig.workingMemoryLimit
            };
        }
        
        async exportMemories() {
            const export_data = {
                workingMemory: Array.from(this.workingMemory.entries()),
                episodicMemory: Array.from(this.episodicMemory.entries()),
                semanticMemory: Array.from(this.semanticMemory.entries()),
                metrics: this.getMemoryMetrics(),
                timestamp: new Date().toISOString()
            };
            
            return export_data;
        }
    }

    // Initialize global Agent Memory System
    window.AgentMemorySystem = new AgentMemorySystem();
})();
// --- END OF FILE agentMemory.js (Manager Class) ---

// --- START OF FILE systemHealth.js (Manager Class) ---
/**
 * Agent Lee System Health Monitor
 * Monitors system performance, resource usage, and component health
 */
(function() {
    class SystemHealthMonitor {
        constructor() {
            this.healthMetrics = {
                cpu: { usage: 0, temperature: 0, cores: navigator.hardwareConcurrency || 4 },
                memory: { used: 0, total: 0, available: 0 },
                network: { latency: 0, bandwidth: 0, status: 'unknown' },
                storage: { used: 0, available: 0, quota: 0 },
                battery: { level: 1, charging: false, chargingTime: 0 },
                performance: { fps: 60, loadTime: 0, responseTime: 0 }
            };
            
            this.healthHistory = [];
            this.alertThresholds = {
                cpu: 80,
                memory: 85,
                storage: 90,
                battery: 20,
                latency: 1000
            };
            
            this.monitoringInterval = null;
            this.isMonitoring = false;
            
            console.log('🏥 System Health Monitor initialized');
            this.startMonitoring();
        }
        
        // ===== MONITORING CONTROL =====
        
        startMonitoring() {
            if (this.isMonitoring) return;
            
            this.isMonitoring = true;
            this.monitoringInterval = setInterval(() => {
                this.collectHealthMetrics();
            }, 5000); // Every 5 seconds
            
            // Initial collection
            this.collectHealthMetrics();
            
            console.log('🏥 System health monitoring started');
        }
        
        stopMonitoring() {
            if (this.monitoringInterval) {
                clearInterval(this.monitoringInterval);
                this.monitoringInterval = null;
            }
            this.isMonitoring = false;
            console.log('🏥 System health monitoring stopped');
        }
        
        // ===== METRICS COLLECTION =====
        
        async collectHealthMetrics() {
            try {
                await Promise.all([
                    this.collectMemoryMetrics(),
                    this.collectStorageMetrics(),
                    this.collectBatteryMetrics(),
                    this.collectNetworkMetrics(),
                    this.collectPerformanceMetrics()
                ]);
                
                // Store in history
                this.healthHistory.push({
                    timestamp: new Date().toISOString(),
                    metrics: JSON.parse(JSON.stringify(this.healthMetrics))
                });
                
                // Keep only last 100 entries
                if (this.healthHistory.length > 100) {
                    this.healthHistory.shift();
                }
                
                // Check for alerts
                this.checkHealthAlerts();
                
                // Broadcast health update
                this.broadcastHealthUpdate();
                
            } catch (error) {
                console.error('❌ Failed to collect health metrics:', error);
            }
        }
        
        async collectMemoryMetrics() {
            if ('memory' in performance) {
                const memory = performance.memory;
                this.healthMetrics.memory = {
                    used: memory.usedJSHeapSize,
                    total: memory.totalJSHeapSize,
                    available: memory.jsHeapSizeLimit - memory.usedJSHeapSize,
                    limit: memory.jsHeapSizeLimit
                };
            }
        }
        
        async collectStorageMetrics() {
            if ('storage' in navigator && 'estimate' in navigator.storage) {
                try {
                    const estimate = await navigator.storage.estimate();
                    this.healthMetrics.storage = {
                        used: estimate.usage || 0,
                        quota: estimate.quota || 0,
                        available: (estimate.quota || 0) - (estimate.usage || 0)
                    };
                } catch (error) {
                    console.warn('Storage metrics unavailable:', error);
                }
            }
        }
        
        async collectBatteryMetrics() {
            if ('getBattery' in navigator) {
                try {
                    const battery = await navigator.getBattery();
                    this.healthMetrics.battery = {
                        level: battery.level,
                        charging: battery.charging,
                        chargingTime: battery.chargingTime,
                        dischargingTime: battery.dischargingTime
                    };
                } catch (error) {
                    console.warn('Battery metrics unavailable:', error);
                }
            }
        }
        
        async collectNetworkMetrics() {
            if ('connection' in navigator) {
                const connection = navigator.connection;
                this.healthMetrics.network = {
                    effectiveType: connection.effectiveType,
                    downlink: connection.downlink,
                    rtt: connection.rtt,
                    saveData: connection.saveData,
                    status: navigator.onLine ? 'online' : 'offline'
                };
            } else {
                this.healthMetrics.network.status = navigator.onLine ? 'online' : 'offline';
            }
            
            // Test network latency
            await this.testNetworkLatency();
        }
        
        async testNetworkLatency() {
            try {
                const start = performance.now();
                await fetch('/', { method: 'HEAD', cache: 'no-cache' });
                const latency = performance.now() - start;
                this.healthMetrics.network.latency = latency;
            } catch (error) {
                this.healthMetrics.network.latency = -1; // Network error
            }
        }
        
        collectPerformanceMetrics() {
            // FPS estimation
            this.healthMetrics.performance.fps = this.estimateFPS();
            
            // Page load time
            if (performance.timing) {
                const timing = performance.timing;
                this.healthMetrics.performance.loadTime = timing.loadEventEnd - timing.navigationStart;
            }
            
            // Response time (from navigation timing)
            if (performance.timing) {
                const timing = performance.timing;
                this.healthMetrics.performance.responseTime = timing.responseEnd - timing.requestStart;
            }
        }
        
        estimateFPS() {
            // Simple FPS estimation based on requestAnimationFrame
            let fps = 60; // Default assumption
            
            if (this.lastFrameTime) {
                const now = performance.now();
                const delta = now - this.lastFrameTime;
                fps = Math.round(1000 / delta);
            }
            
            this.lastFrameTime = performance.now();
            return Math.min(fps, 60); // Cap at 60 FPS
        }
        
        // ===== HEALTH ALERTS =====
        
        checkHealthAlerts() {
            const alerts = [];
            
            // Memory usage alert
            if (this.healthMetrics.memory.total > 0) {
                const memoryUsage = (this.healthMetrics.memory.used / this.healthMetrics.memory.total) * 100;
                if (memoryUsage > this.alertThresholds.memory) {
                    alerts.push({
                        type: 'memory',
                        severity: 'warning',
                        message: `High memory usage: ${memoryUsage.toFixed(1)}%`,
                        value: memoryUsage
                    });
                }
            }
            
            // Storage usage alert
            if (this.healthMetrics.storage.quota > 0) {
                const storageUsage = (this.healthMetrics.storage.used / this.healthMetrics.storage.quota) * 100;
                if (storageUsage > this.alertThresholds.storage) {
                    alerts.push({
                        type: 'storage',
                        severity: 'warning',
                        message: `High storage usage: ${storageUsage.toFixed(1)}%`,
                        value: storageUsage
                    });
                }
            }
            
            // Battery level alert
            if (this.healthMetrics.battery.level < this.alertThresholds.battery / 100) {
                alerts.push({
                    type: 'battery',
                    severity: 'warning',
                    message: `Low battery: ${(this.healthMetrics.battery.level * 100).toFixed(0)}%`,
                    value: this.healthMetrics.battery.level * 100
                });
            }
            
            // Network latency alert
            if (this.healthMetrics.network.latency > this.alertThresholds.latency) {
                alerts.push({
                    type: 'network',
                    severity: 'warning',
                    message: `High network latency: ${this.healthMetrics.network.latency.toFixed(0)}ms`,
                    value: this.healthMetrics.network.latency
                });
            }
            
            // Process alerts
            if (alerts.length > 0) {
                this.processHealthAlerts(alerts);
            }
        }
        
        processHealthAlerts(alerts) {
            for (const alert of alerts) {
                console.warn(`🏥 Health Alert [${alert.type}]: ${alert.message}`);
                
                // Store alert in database if available
                if (window.AgentLeeDBHelpers) {
                    window.AgentLeeDBHelpers.addRecord('diagnostics', {
                        diagnostic_id: `health_alert_${Date.now()}`,
                        module_name: 'system_health',
                        health_score: 100 - alert.value,
                        status: alert.severity,
                        timestamp: new Date().toISOString(),
                        metadata: {
                            alert_type: alert.type,
                            message: alert.message,
                            value: alert.value
                        }
                    }).catch(error => {
                        console.error('Failed to store health alert:', error);
                    });
                }
            }
        }
        
        // ===== EVENT BROADCASTING =====
        
        broadcastHealthUpdate() {
            if (window.AgentLeeEventBus) {
                window.AgentLeeEventBus.postMessage({
                    type: 'SystemHealthUpdate',
                    data: {
                        metrics: this.healthMetrics,
                        timestamp: new Date().toISOString(),
                        status: this.getOverallHealthStatus()
                    }
                });
            }
        }
        
        getOverallHealthStatus() {
            let score = 100;
            
            // Deduct points for various issues
            if (this.healthMetrics.memory.total > 0) {
                const memoryUsage = (this.healthMetrics.memory.used / this.healthMetrics.memory.total) * 100;
                if (memoryUsage > 70) score -= (memoryUsage - 70);
            }
            
            if (this.healthMetrics.network.latency > 500) {
                score -= Math.min((this.healthMetrics.network.latency - 500) / 10, 20);
            }
            
            if (this.healthMetrics.battery.level < 0.3) {
                score -= (0.3 - this.healthMetrics.battery.level) * 100;
            }
            
            score = Math.max(score, 0);
            
            if (score >= 90) return 'excellent';
            if (score >= 75) return 'good';
            if (score >= 50) return 'fair';
            if (score >= 25) return 'poor';
            return 'critical';
        }
        
        // ===== PUBLIC API =====
        
        getHealthMetrics() {
            return {
                current: this.healthMetrics,
                history: this.healthHistory,
                status: this.getOverallHealthStatus(),
                isMonitoring: this.isMonitoring
            };
        }
        
        getHealthSummary() {
            const metrics = this.healthMetrics;
            const summary = {
                overall_status: this.getOverallHealthStatus(),
                memory_usage: metrics.memory.total > 0 ? 
                    ((metrics.memory.used / metrics.memory.total) * 100).toFixed(1) + '%' : 'Unknown',
                storage_usage: metrics.storage.quota > 0 ? 
                    ((metrics.storage.used / metrics.storage.quota) * 100).toFixed(1) + '%' : 'Unknown',
                network_status: metrics.network.status,
                network_latency: metrics.network.latency > 0 ? 
                    metrics.network.latency.toFixed(0) + 'ms' : 'Unknown',
                battery_level: (metrics.battery.level * 100).toFixed(0) + '%',
                performance_fps: metrics.performance.fps + ' FPS'
            };
            
            return summary;
        }
        
        async exportHealthData() {
            return {
                metrics: this.healthMetrics,
                history: this.healthHistory,
                thresholds: this.alertThresholds,
                status: this.getOverallHealthStatus(),
                timestamp: new Date().toISOString()
            };
        }
        
        setAlertThreshold(metric, value) {
            if (this.alertThresholds.hasOwnProperty(metric)) {
                this.alertThresholds[metric] = value;
                console.log(`🏥 Alert threshold updated: ${metric} = ${value}`);
            }
        }
    }

    // Initialize global System Health Monitor
    window.SystemHealthMonitor = new SystemHealthMonitor();
})();
// --- END OF FILE systemHealth.js (Manager Class) ---

// --- START OF FILE syncData.js (Manager Class) ---
/**
 * Agent Lee Data Synchronization System
 * Handles data sync between frontend, backend, and database
 */
(function() {
    class DataSynchronizationManager {
        constructor() {
            this.syncQueue = [];
            this.syncInProgress = false;
            this.syncInterval = null;
            this.lastSyncTime = null;
            this.syncMetrics = {
                totalSyncs: 0,
                successfulSyncs: 0,
                failedSyncs: 0,
                averageSyncTime: 0,
                lastError: null
            };
            
            // Sync configuration
            this.syncConfig = {
                autoSyncInterval: 30000, // 30 seconds
                batchSize: 50,
                retryAttempts: 3,
                retryDelay: 5000,
                conflictResolution: 'latest_wins' // 'latest_wins', 'merge', 'manual'
            };
            
            // Data sources to sync
            this.dataSources = {
                'agents': { priority: 1, lastSync: null },
                'tasks': { priority: 1, lastSync: null },
                'workers': { priority: 2, lastSync: null },
                'llm_sessions': { priority: 2, lastSync: null },
                'memory_fragments': { priority: 3, lastSync: null },
                'telemetry': { priority: 4, lastSync: null },
                'execution_logs': { priority: 4, lastSync: null }
            };
            
            console.log('🔄 Data Synchronization Manager initialized');
            this.startAutoSync();
        }
        
        // ===== SYNC QUEUE MANAGEMENT =====
        
        queueSync(dataType, operation, data, priority = 0.5) {
            const syncItem = {
                id: `sync_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
                dataType,
                operation, // 'create', 'update', 'delete', 'bulk_sync'
                data,
                priority,
                timestamp: new Date().toISOString(),
                attempts: 0,
                status: 'queued'
            };
            
            this.syncQueue.push(syncItem);
            
            // Sort queue by priority (higher priority first)
            this.syncQueue.sort((a, b) => b.priority - a.priority);
            
            console.log(`🔄 Queued sync: ${dataType} ${operation} (priority: ${priority})`);
            
            // Trigger immediate sync for high priority items
            if (priority > 0.8 && !this.syncInProgress) {
                this.processSyncQueue();
            }
            
            return syncItem.id;
        }
        
        // ===== SYNC PROCESSING =====
        
        async processSyncQueue() {
            if (this.syncInProgress || this.syncQueue.length === 0) {
                return;
            }
            
            this.syncInProgress = true;
            const syncStartTime = Date.now();
            
            console.log(`🔄 Processing sync queue: ${this.syncQueue.length} items`);
            
            try {
                // Process items in batches
                const batch = this.syncQueue.splice(0, this.syncConfig.batchSize);
                const syncPromises = batch.map(item => this.processSyncItem(item));
                
                const results = await Promise.allSettled(syncPromises);
                
                // Update metrics
                const successful = results.filter(r => r.status === 'fulfilled').length;
                const failed = results.filter(r => r.status === 'rejected').length;
                
                this.syncMetrics.totalSyncs += batch.length;
                this.syncMetrics.successfulSyncs += successful;
                this.syncMetrics.failedSyncs += failed;
                
                const syncTime = Date.now() - syncStartTime;
                this.syncMetrics.averageSyncTime = (this.syncMetrics.averageSyncTime + syncTime) / 2;
                this.lastSyncTime = new Date().toISOString();
                
                console.log(`✅ Sync batch complete: ${successful} successful, ${failed} failed`);
                
                // Re-queue failed items with exponential backoff
                for (let i = 0; i < results.length; i++) {
                    if (results[i].status === 'rejected') {
                        const item = batch[i];
                        item.attempts++;
                        
                        if (item.attempts < this.syncConfig.retryAttempts) {
                            item.status = 'retry';
                            item.nextRetry = Date.now() + (this.syncConfig.retryDelay * Math.pow(2, item.attempts));
                            this.syncQueue.push(item);
                            console.log(`🔄 Re-queued failed sync: ${item.id} (attempt ${item.attempts})`);
                        } else {
                            console.error(`❌ Sync permanently failed: ${item.id}`);
                            this.syncMetrics.lastError = results[i].reason;
                        }
                    }
                }
                
            } catch (error) {
                console.error('❌ Sync queue processing error:', error);
                this.syncMetrics.lastError = error.message;
            } finally {
                this.syncInProgress = false;
                
                // Continue processing if more items in queue
                if (this.syncQueue.length > 0) {
                    setTimeout(() => this.processSyncQueue(), 1000);
                }
            }
        }
        
        async processSyncItem(item) {
            console.log(`🔄 Processing sync item: ${item.dataType} ${item.operation}`);
            
            try {
                item.status = 'processing';
                
                switch (item.operation) {
                    case 'create':
                        await this.syncCreate(item);
                        break;
                    case 'update':
                        await this.syncUpdate(item);
                        break;
                    case 'delete':
                        await this.syncDelete(item);
                        break;
                    case 'bulk_sync':
                        await this.syncBulk(item);
                        break;
                    default:
                        throw new Error(`Unknown sync operation: ${item.operation}`);
                }
                
                item.status = 'completed';
                item.completedAt = new Date().toISOString();
                
                // Update data source sync time
                if (this.dataSources[item.dataType]) {
                    this.dataSources[item.dataType].lastSync = item.completedAt;
                }
                
                console.log(`✅ Sync completed: ${item.id}`);
                
            } catch (error) {
                item.status = 'failed';
                item.error = error.message;
                console.error(`❌ Sync failed: ${item.id}`, error);
                throw error;
            }
        }
        
        // ===== SYNC OPERATIONS =====
        
        async syncCreate(item) {
            const { dataType, data } = item;
            
            // Store in local database
            if (window.AgentLeeDBHelpers) {
                await window.AgentLeeDBHelpers.addRecord(dataType, data);
            }
            
            // Sync to backend if available
            await this.syncToBackend('POST', dataType, data);
            
            // Broadcast sync event
            this.broadcastSyncEvent('create', dataType, data);
        }
        
        async syncUpdate(item) {
            const { dataType, data } = item;
            
            // Update in local database
            if (window.AgentLeeDBHelpers && data.id) {
                await window.AgentLeeDBHelpers.updateRecord(dataType, data.id, data);
            }
            
            // Sync to backend if available
            await this.syncToBackend('PUT', `${dataType}/${data.id}`, data);
            
            // Broadcast sync event
            this.broadcastSyncEvent('update', dataType, data);
        }
        
        async syncDelete(item) {
            const { dataType, data } = item;
            
            // Delete from local database
            if (window.AgentLeeDBHelpers && data.id) {
                await window.AgentLeeDBHelpers.deleteRecord(dataType, data.id);
            }
            
            // Sync to backend if available
            await this.syncToBackend('DELETE', `${dataType}/${data.id}`);
            
            // Broadcast sync event
            this.broadcastSyncEvent('delete', dataType, data);
        }
        
        async syncBulk(item) {
            const { dataType, data } = item;
            
            if (!Array.isArray(data)) {
                throw new Error('Bulk sync data must be an array');
            }
            
            // Process in smaller chunks
            const chunkSize = 10;
            for (let i = 0; i < data.length; i += chunkSize) {
                const chunk = data.slice(i, i + chunkSize);
                
                // Bulk insert/update in database
                if (window.AgentLeeDBHelpers) {
                    for (const record of chunk) {
                        try {
                            await window.AgentLeeDBHelpers.addRecord(dataType, record);
                        } catch (error) {
                            // Try update if create fails (record might exist)
                            if (record.id) {
                                await window.AgentLeeDBHelpers.updateRecord(dataType, record.id, record);
                            }
                        }
                    }
                }
                
                // Sync chunk to backend
                await this.syncToBackend('POST', `${dataType}/bulk`, chunk);
            }
            
            // Broadcast bulk sync event
            this.broadcastSyncEvent('bulk_sync', dataType, { count: data.length });
        }
        
        // ===== BACKEND SYNCHRONIZATION =====
        
        async syncToBackend(method, endpoint, data = null) {
            // Check if backend sync is available
            if (!this.isBackendAvailable()) {
                console.log('Backend not available, skipping backend sync');
                return;
            }
            
            try {
                const url = `http://localhost:8000/api/${endpoint}`;
                const options = {
                    method,
                    headers: {
                        'Content-Type': 'application/json',
                        'X-Sync-Source': 'agent-lee-frontend'
                    }
                };
                
                if (data && ['POST', 'PUT', 'PATCH'].includes(method)) {
                    options.body = JSON.stringify(data);
                }
                
                const response = await fetch(url, options);
                
                if (!response.ok) {
                    throw new Error(`Backend sync failed: ${response.status} ${response.statusText}`);
                }
                
                const result = await response.json();
                console.log(`✅ Backend sync successful: ${method} ${endpoint}`);
                
                return result;
                
            } catch (error) {
                console.warn(`⚠️ Backend sync failed: ${error.message}`);
                // Don't throw error - frontend should continue working without backend
            }
        }
        
        isBackendAvailable() {
            // Simple check - could be enhanced with actual health check
            return typeof fetch !== 'undefined';
        }
        
        // ===== AUTO SYNC =====
        
        startAutoSync() {
            this.syncInterval = setInterval(() => {
                this.performAutoSync();
            }, this.syncConfig.autoSyncInterval);
            
            console.log(`🔄 Auto-sync started: ${this.syncConfig.autoSyncInterval}ms interval`);
        }
        
        async performAutoSync() {
            console.log('🔄 Performing auto-sync...');
            
            try {
                // Sync each data source based on priority
                const sortedSources = Object.entries(this.dataSources)
                    .sort(([,a], [,b]) => a.priority - b.priority);
                
                for (const [dataType, config] of sortedSources) {
                    await this.syncDataSource(dataType);
                }
                
                // Process any queued sync items
                if (this.syncQueue.length > 0) {
                    await this.processSyncQueue();
                }
                
            } catch (error) {
                console.error('❌ Auto-sync error:', error);
            }
        }
        
        async syncDataSource(dataType) {
            if (!window.AgentLeeDBHelpers) {
                return;
            }
            
            try {
                // Get all records of this type
                const records = await window.AgentLeeDBHelpers.getAllRecords(dataType);
                
                if (records.length > 0) {
                    // Queue bulk sync
                    this.queueSync(dataType, 'bulk_sync', records, 0.3);
                }
                
            } catch (error) {
                console.error(`Failed to sync data source ${dataType}:`, error);
            }
        }
        
        // ===== CONFLICT RESOLUTION =====
        
        async resolveConflict(localData, remoteData, strategy = null) {
            const resolutionStrategy = strategy || this.syncConfig.conflictResolution;
            
            switch (resolutionStrategy) {
                case 'latest_wins':
                    const localTime = new Date(localData.updated_at || localData.timestamp || 0);
                    const remoteTime = new Date(remoteData.updated_at || remoteData.timestamp || 0);
                    return localTime > remoteTime ? localData : remoteData;
                    
                case 'merge':
                    return { ...remoteData, ...localData };
                    
                case 'manual':
                    // Queue for manual resolution
                    this.queueManualResolution(localData, remoteData);
                    return localData; // Keep local for now
                    
                default:
                    return localData;
            }
        }
        
        queueManualResolution(localData, remoteData) {
            // This would typically show a UI for manual conflict resolution
            console.log('⚠️ Manual conflict resolution required:', { localData, remoteData });
            
            // Broadcast conflict event
            if (window.AgentLeeEventBus) {
                window.AgentLeeEventBus.postMessage({
                    type: 'SyncConflict',
                    data: {
                        localData,
                        remoteData,
                        timestamp: new Date().toISOString()
                    }
                });
            }
        }
        
        // ===== EVENT BROADCASTING =====
        
        broadcastSyncEvent(operation, dataType, data) {
            if (window.AgentLeeEventBus) {
                window.AgentLeeEventBus.postMessage({
                    type: 'DataSynced',
                    data: {
                        operation,
                        dataType,
                        data,
                        timestamp: new Date().toISOString()
                    }
                });
            }
        }
        
        // ===== PUBLIC API =====
        
        async forceSyncAll() {
            console.log('🔄 Force syncing all data sources...');
            
            for (const dataType of Object.keys(this.dataSources)) {
                await this.syncDataSource(dataType);
            }
            
            await this.processSyncQueue();
            
            console.log('✅ Force sync complete');
        }
        
        getSyncMetrics() {
            return {
                ...this.syncMetrics,
                queueLength: this.syncQueue.length,
                syncInProgress: this.syncInProgress,
                lastSyncTime: this.lastSyncTime,
                dataSources: this.dataSources
            };
        }
        
        pauseAutoSync() {
            if (this.syncInterval) {
                clearInterval(this.syncInterval);
                this.syncInterval = null;
                console.log('⏸️ Auto-sync paused');
            }
        }
        
        resumeAutoSync() {
            if (!this.syncInterval) {
                this.startAutoSync();
                console.log('▶️ Auto-sync resumed');
            }
        }
        
        clearSyncQueue() {
            this.syncQueue = [];
            console.log('🗑️ Sync queue cleared');
        }
        
        async exportSyncData() {
            return {
                syncQueue: this.syncQueue,
                syncMetrics: this.syncMetrics,
                dataSources: this.dataSources,
                config: this.syncConfig,
                timestamp: new Date().toISOString()
            };
        }
    }

    // Initialize global Data Synchronization Manager
    window.DataSynchronizationManager = new DataSynchronizationManager();
})();
// --- END OF FILE syncData.js (Manager Class) ---

// --- START OF FILE schema_migration_system.js ---
/**
 * Agent Lee Database Schema Migration System
 * Handles version upgrades and data migration for AgentLeeDB
 */
(function() {
    class AgentLeeSchemaManager {
        constructor() {
            this.currentVersion = 3;
            this.migrations = new Map();
            this.backupEnabled = true;
            
            // Register all migrations
            this.registerMigrations();
            
            console.log('🔄 Schema Manager initialized');
        }
        
        // ===== MIGRATION REGISTRATION =====
        
        registerMigrations() {
            // Migration from version 1 to 2
            this.migrations.set(2, {
                description: 'Add LLM tracking and telemetry tables',
                migrate: this.migrateToV2.bind(this)
            });
            
            // Migration from version 2 to 3
            this.migrations.set(3, {
                description: 'Add comprehensive schema with 22 tables',
                migrate: this.migrateToV3.bind(this)
            });
            
            // Future migrations can be added here
            // this.migrations.set(4, {
            //     description: 'Add new feature tables',
            //     migrate: this.migrateToV4.bind(this)
            // });
        }
        
        // ===== MIGRATION IMPLEMENTATIONS =====
        
        async migrateToV2(db, transaction) {
            console.log('🔄 Migrating to schema version 2...');
            
            // Add LLM sessions table if not exists
            if (!db.objectStoreNames.contains('llm_sessions')) {
                const sessions = db.createObjectStore('llm_sessions', { keyPath: 'session_id' });
                sessions.createIndex('timestamp', 'timestamp', { unique: false });
                sessions.createIndex('context_scope', 'context_scope', { multiEntry: true });
                sessions.createIndex('llm_type', 'llm_type', { unique: false });
            }
            
            // Add telemetry table if not exists
            if (!db.objectStoreNames.contains('telemetry')) {
                const telemetry = db.createObjectStore('telemetry', { keyPath: 'entry_id' });
                telemetry.createIndex('region', 'region', { unique: false });
                telemetry.createIndex('timestamp', 'timestamp', { unique: false });
                telemetry.createIndex('event_type', 'event_type', { unique: false });
            }
            
            console.log('✅ Migration to version 2 complete');
        }
        
        async migrateToV3(db, transaction) {
            console.log('🔄 Migrating to schema version 3...');
            
            // Add all new tables for comprehensive schema
            const newTables = [
                {
                    name: 'agent_workflows',
                    keyPath: 'workflow_id',
                    indexes: [
                        { name: 'agent_id', keyPath: 'agent_id', unique: false },
                        { name: 'trigger_type', keyPath: 'trigger_type', unique: false },
                        { name: 'status', keyPath: 'status', unique: false }
                    ]
                },
                {
                    name: 'azr_nodes',
                    keyPath: 'node_id',
                    indexes: [
                        { name: 'vector_hash', keyPath: 'vector_hash', unique: false },
                        { name: 'entropy_score', keyPath: 'entropy_score', unique: false },
                        { name: 'linked_nodes', keyPath: 'linked_nodes', multiEntry: true },
                        { name: 'last_accessed', keyPath: 'last_accessed', unique: false }
                    ]
                },
                {
                    name: 'execution_logs',
                    keyPath: 'log_id',
                    indexes: [
                        { name: 'executor_id', keyPath: 'executor_id', unique: false },
                        { name: 'command_type', keyPath: 'command_type', unique: false },
                        { name: 'timestamp', keyPath: 'timestamp', unique: false },
                        { name: 'success', keyPath: 'success', unique: false }
                    ]
                },
                {
                    name: 'healing_logs',
                    keyPath: 'healing_id',
                    indexes: [
                        { name: 'component_id', keyPath: 'component_id', unique: false },
                        { name: 'severity', keyPath: 'severity', unique: false },
                        { name: 'repair_type', keyPath: 'repair_type', unique: false },
                        { name: 'timestamp', keyPath: 'timestamp', unique: false }
                    ]
                },
                {
                    name: 'diagnostics',
                    keyPath: 'diagnostic_id',
                    indexes: [
                        { name: 'module_name', keyPath: 'module_name', unique: false },
                        { name: 'health_score', keyPath: 'health_score', unique: false },
                        { name: 'timestamp', keyPath: 'timestamp', unique: false },
                        { name: 'status', keyPath: 'status', unique: false }
                    ]
                },
                {
                    name: 'gui_registry',
                    keyPath: 'gui_id',
                    indexes: [
                        { name: 'region', keyPath: 'region', unique: false },
                        { name: 'status', keyPath: 'status', unique: false },
                        { name: 'position_x', keyPath: 'position_x', unique: false },
                        { name: 'position_y', keyPath: 'position_y', unique: false }
                    ]
                },
                {
                    name: 'speech_styles',
                    keyPath: 'style_id',
                    indexes: [
                        { name: 'agent_id', keyPath: 'agent_id', unique: false },
                        { name: 'tone', keyPath: 'tone', unique: false },
                        { name: 'context', keyPath: 'context', unique: false }
                    ]
                },
                {
                    name: 'engagement_signals',
                    keyPath: 'signal_id',
                    indexes: [
                        { name: 'user_id', keyPath: 'user_id', unique: false },
                        { name: 'signal_type', keyPath: 'signal_type', unique: false },
                        { name: 'timestamp', keyPath: 'timestamp', unique: false },
                        { name: 'intensity', keyPath: 'intensity', unique: false }
                    ]
                },
                {
                    name: 'user_behavior',
                    keyPath: 'behavior_id',
                    indexes: [
                        { name: 'user_id', keyPath: 'user_id', unique: false },
                        { name: 'attitude', keyPath: 'attitude', unique: false },
                        { name: 'hesitancy_level', keyPath: 'hesitancy_level', unique: false },
                        { name: 'tone_pattern', keyPath: 'tone_pattern', unique: false }
                    ]
                },
                {
                    name: 'memory_fragments',
                    keyPath: 'fragment_id',
                    indexes: [
                        { name: 'memory_type', keyPath: 'memory_type', unique: false },
                        { name: 'relevance_score', keyPath: 'relevance_score', unique: false },
                        { name: 'last_accessed', keyPath: 'last_accessed', unique: false },
                        { name: 'emotional_weight', keyPath: 'emotional_weight', unique: false }
                    ]
                },
                {
                    name: 'learning_models',
                    keyPath: 'model_id',
                    indexes: [
                        { name: 'model_type', keyPath: 'model_type', unique: false },
                        { name: 'accuracy_score', keyPath: 'accuracy_score', unique: false },
                        { name: 'last_updated', keyPath: 'last_updated', unique: false },
                        { name: 'training_cycles', keyPath: 'training_cycles', unique: false }
                    ]
                },
                {
                    name: 'local_db_refs',
                    keyPath: 'ref_id',
                    indexes: [
                        { name: 'db_name', keyPath: 'db_name', unique: false },
                        { name: 'creator_agent', keyPath: 'creator_agent', unique: false },
                        { name: 'purpose', keyPath: 'purpose', unique: false },
                        { name: 'created_at', keyPath: 'created_at', unique: false }
                    ]
                },
                {
                    name: 'knowledge_updates',
                    keyPath: 'update_id',
                    indexes: [
                        { name: 'update_type', keyPath: 'update_type', unique: false },
                        { name: 'source', keyPath: 'source', unique: false },
                        { name: 'confidence', keyPath: 'confidence', unique: false },
                        { name: 'timestamp', keyPath: 'timestamp', unique: false }
                    ]
                },
                {
                    name: 'motivation_triggers',
                    keyPath: 'trigger_id',
                    indexes: [
                        { name: 'trigger_type', keyPath: 'trigger_type', unique: false },
                        { name: 'user_response', keyPath: 'user_response', unique: false },
                        { name: 'effectiveness', keyPath: 'effectiveness', unique: false },
                        { name: 'context', keyPath: 'context', unique: false }
                    ]
                },
                {
                    name: 'emotion_tracks',
                    keyPath: 'emotion_id',
                    indexes: [
                        { name: 'user_id', keyPath: 'user_id', unique: false },
                        { name: 'emotion_type', keyPath: 'emotion_type', unique: false },
                        { name: 'intensity', keyPath: 'intensity', unique: false },
                        { name: 'timestamp', keyPath: 'timestamp', unique: false },
                        { name: 'source_input', keyPath: 'source_input', unique: false }
                    ]
                },
                {
                    name: 'agent_moods',
                    keyPath: 'mood_id',
                    indexes: [
                        { name: 'agent_id', keyPath: 'agent_id', unique: false },
                        { name: 'mood_type', keyPath: 'mood_type', unique: false },
                        { name: 'context_trigger', keyPath: 'context_trigger', unique: false },
                        { name: 'duration', keyPath: 'duration', unique: false },
                        { name: 'timestamp', keyPath: 'timestamp', unique: false }
                    ]
                },
                {
                    name: 'reflection_protocols',
                    keyPath: 'reflection_id',
                    indexes: [
                        { name: 'agent_id', keyPath: 'agent_id', unique: false },
                        { name: 'reflection_type', keyPath: 'reflection_type', unique: false },
                        { name: 'trigger_event', keyPath: 'trigger_event', unique: false },
                        { name: 'pause_duration', keyPath: 'pause_duration', unique: false },
                        { name: 'timestamp', keyPath: 'timestamp', unique: false }
                    ]
                }
            ];
            
            // Create new tables
            for (const tableConfig of newTables) {
                if (!db.objectStoreNames.contains(tableConfig.name)) {
                    const store = db.createObjectStore(tableConfig.name, { keyPath: tableConfig.keyPath });
                    
                    // Add indexes
                    for (const indexConfig of tableConfig.indexes) {
                        store.createIndex(indexConfig.name, indexConfig.keyPath, {
                            unique: indexConfig.unique || false,
                            multiEntry: indexConfig.multiEntry || false
                        });
                    }
                    
                    console.log(`✅ Created table: ${tableConfig.name}`);
                }
            }
            
            console.log('✅ Migration to version 3 complete');
        }
        
        // ===== BACKUP SYSTEM =====
        
        async createBackup(db) {
            if (!this.backupEnabled) return null;
            
            console.log('💾 Creating database backup...');
            
            const backup = {
                version: db.version,
                timestamp: new Date().toISOString(),
                tables: {}
            };
            
            try {
                // Export all data from all tables
                for (const tableName of db.objectStoreNames) {
                    const transaction = db.transaction([tableName], 'readonly');
                    const store = transaction.objectStore(tableName);
                    const request = store.getAll();
                    
                    const data = await new Promise((resolve, reject) => {
                        request.onsuccess = () => resolve(request.result);
                        request.onerror = () => reject(request.error);
                    });
                    
                    backup.tables[tableName] = data;
                }
                
                // Save backup to localStorage (with size limit consideration)
                const backupString = JSON.stringify(backup);
                const backupSize = new Blob([backupString]).size;
                
                if (backupSize < 5 * 1024 * 1024) { // 5MB limit
                    localStorage.setItem(`agentlee_backup_${backup.timestamp}`, backupString);
                    console.log(`✅ Backup created: ${(backupSize / 1024).toFixed(2)}KB`);
                } else {
                    console.warn('⚠️ Backup too large for localStorage, offering download...');
                    this.downloadBackup(backup);
                }
                
                return backup;
                
            } catch (error) {
                console.error('❌ Backup creation failed:', error);
                return null;
            }
        }
        
        downloadBackup(backup) {
            const blob = new Blob([JSON.stringify(backup, null, 2)], { type: 'application/json' });
            const url = URL.createObjectURL(blob);
            
            const a = document.createElement('a');
            a.href = url;
            a.download = `agentlee_backup_${backup.timestamp.replace(/[:.]/g, '-')}.json`;
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            URL.revokeObjectURL(url);
        }
        
        async restoreFromBackup(backupData) {
            console.log('🔄 Restoring from backup...');
            
            try {
                // This would require recreating the database
                // Implementation depends on specific restore requirements
                console.log('📋 Backup restore functionality ready for implementation');
                return true;
            } catch (error) {
                console.error('❌ Restore failed:', error);
                return false;
            }
        }
        
        // ===== MIGRATION ORCHESTRATION =====
        
        async performMigration(db, oldVersion, newVersion, transaction) {
            console.log(`🔄 Migrating database from version ${oldVersion} to ${newVersion}`);
            
            // Create backup before migration
            if (oldVersion > 0) {
                await this.createBackup(db);
            }
            
            // Apply migrations sequentially
            for (let version = oldVersion + 1; version <= newVersion; version++) {
                if (this.migrations.has(version)) {
                    const migration = this.migrations.get(version);
                    console.log(`🔄 Applying migration ${version}: ${migration.description}`);
                    
                    try {
                        await migration.migrate(db, transaction);
                        console.log(`✅ Migration ${version} completed successfully`);
                    } catch (error) {
                        console.error(`❌ Migration ${version} failed:`, error);
                        throw error;
                    }
                }
            }
            
            // Log migration completion
            if (window.AgentLeeDBHelpers) {
                try {
                    await window.AgentLeeDBHelpers.addRecord('execution_logs', {
                        log_id: `migration_${Date.now()}`,
                        executor_id: 'schema_manager',
                        command_type: 'database_migration',
                        timestamp: new Date().toISOString(),
                        success: true,
                        metadata: {
                            old_version: oldVersion,
                            new_version: newVersion,
                            migration_timestamp: new Date().toISOString()
                        }
                    });
                } catch (error) {
                    console.warn('Failed to log migration:', error);
                }
            }
            
            console.log(`🎉 Database migration completed: v${oldVersion} → v${newVersion}`);
        }
        
        // ===== SCHEMA VALIDATION =====
        
        async validateSchema(db) {
            const expectedTables = [
                'agents', 'agent_workflows', 'tasks', 'workers', 'llm_sessions',
                'azr_nodes', 'execution_logs', 'healing_logs', 'diagnostics',
                'gui_registry', 'telemetry', 'speech_styles', 'engagement_signals',
                'user_behavior', 'memory_fragments', 'learning_models', 'local_db_refs',
                'knowledge_updates', 'motivation_triggers', 'emotion_tracks',
                'agent_moods', 'reflection_protocols'
            ];
            
            const actualTables = Array.from(db.objectStoreNames);
            const missingTables = expectedTables.filter(table => !actualTables.includes(table));
            const extraTables = actualTables.filter(table => !expectedTables.includes(table));
            
            const validation = {
                valid: missingTables.length === 0,
                version: db.version,
                expected_tables: expectedTables.length,
                actual_tables: actualTables.length,
                missing_tables: missingTables,
                extra_tables: extraTables,
                timestamp: new Date().toISOString()
            };
            
            if (validation.valid) {
                console.log(`✅ Schema validation passed - v${db.version} with ${actualTables.length} tables`);
            } else {
                console.error(`❌ Schema validation failed - Missing: ${missingTables.join(', ')}`);
            }
            
            return validation;
        }
        
        // ===== PUBLIC API =====
        
        getCurrentVersion() {
            return this.currentVersion;
        }
        
        setBackupEnabled(enabled) {
            this.backupEnabled = enabled;
            console.log(`💾 Backup ${enabled ? 'enabled' : 'disabled'}`);
        }
        
        listAvailableBackups() {
            const backups = [];
            for (let i = 0; i < localStorage.length; i++) {
                const key = localStorage.key(i);
                if (key && key.startsWith('agentlee_backup_')) {
                    const timestamp = key.replace('agentlee_backup_', '');
                    backups.push({
                        key,
                        timestamp,
                        size: localStorage.getItem(key)?.length || 0
                    });
                }
            }
            return backups.sort((a, b) => b.timestamp.localeCompare(a.timestamp));
        }
        
        cleanupOldBackups(keepCount = 5) {
            const backups = this.listAvailableBackups();
            if (backups.length > keepCount) {
                const toDelete = backups.slice(keepCount);
                for (const backup of toDelete) {
                    localStorage.removeItem(backup.key);
                    console.log(`🗑️ Removed old backup: ${backup.timestamp}`);
                }
            }
        }
    }

    // Initialize global schema manager
    window.AgentLeeSchemaManager = new AgentLeeSchemaManager();
})();
// --- END OF FILE schema_migration_system.js ---

// --- START OF FILE production_monitoring.js ---
/**
 * Agent Lee Production Monitoring & Error Reporting System
 * Comprehensive logging, performance monitoring, and error tracking
 */
(function() {
    class AgentLeeProductionMonitor {
        constructor() {
            this.sessionId = `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
            this.startTime = Date.now();
            this.errorCount = 0;
            this.performanceMetrics = new Map();
            this.userFeedback = [];
            
            // Initialize monitoring
            this.initializeErrorHandling();
            this.initializePerformanceMonitoring();
            this.initializeUserFeedback();
            
            console.log(`🔍 Production Monitor initialized - Session: ${this.sessionId}`);
        }
        
        // ===== ERROR HANDLING & LOGGING =====
        
        initializeErrorHandling() {
            // Global error handler
            window.addEventListener('error', (event) => {
                this.logError('JavaScript Error', {
                    message: event.message,
                    filename: event.filename,
                    lineno: event.lineno,
                    colno: event.colno,
                    stack: event.error?.stack
                });
            });
            
            // Unhandled promise rejections
            window.addEventListener('unhandledrejection', (event) => {
                this.logError('Unhandled Promise Rejection', {
                    reason: event.reason,
                    promise: event.promise
                });
            });
            
            // Console error interception
            const originalConsoleError = console.error;
            console.error = (...args) => {
                this.logError('Console Error', { args: args.map(arg => String(arg)) });
                originalConsoleError.apply(console, args);
            };
        }
        
        async logError(type, details) {
            this.errorCount++;
            
            const errorLog = {
                log_id: `error_${Date.now()}_${this.errorCount}`,
                session_id: this.sessionId,
                error_type: type,
                timestamp: new Date().toISOString(),
                details: details,
                user_agent: navigator.userAgent,
                url: window.location.href,
                memory_usage: this.getMemoryUsage(),
                loaded_guis: window.MicrofrontendLoader?.getLoadedGuis() || [],
                llm_status: this.getLLMStatus()
            };
            
            // Log to local database
            try {
                if (window.AgentLeeDBHelpers) {
                    await window.AgentLeeDBHelpers.addRecord('execution_logs', {
                        log_id: errorLog.log_id,
                        executor_id: 'production_monitor',
                        command_type: 'error_log',
                        timestamp: errorLog.timestamp,
                        success: false,
                        error_details: errorLog,
                        metadata: { session_id: this.sessionId }
                    });
                }
            } catch (dbError) {
                console.warn('Failed to log error to database:', dbError);
            }
            
            // Send to remote logging service (if configured)
            this.sendToRemoteLogging(errorLog);
            
            // Critical error notification
            if (this.errorCount > 5) {
                this.showCriticalErrorNotification();
            }
        }
        
        sendToRemoteLogging(errorLog) {
            // Placeholder for remote logging service
            // In production, this would send to Sentry, Logtail, or custom endpoint
            
            // Example implementation:
            /*
            try {
                fetch('https://your-logging-endpoint.com/errors', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify(errorLog)
                }).catch(err => console.warn('Remote logging failed:', err));
            } catch (err) {
                console.warn('Remote logging error:', err);
            }
            */
            
            console.log('📡 Error logged (remote logging disabled):', errorLog);
        }
        
        // ===== PERFORMANCE MONITORING =====
        
        initializePerformanceMonitoring() {
            // Performance observer for monitoring
            if ('PerformanceObserver' in window) {
                const observer = new PerformanceObserver((list) => {
                    for (const entry of list.getEntries()) {
                        this.recordPerformanceMetric(entry.name, entry.duration, entry.entryType);
                    }
                });
                
                observer.observe({ entryTypes: ['measure', 'navigation', 'resource'] });
            }
            
            // Monitor LLM inference times
            this.monitorLLMPerformance();
            
            // Monitor database operations
            this.monitorDatabasePerformance();
            
            // Memory usage monitoring
            setInterval(() => {
                this.recordMemoryUsage();
            }, 30000); // Every 30 seconds
        }
        
        recordPerformanceMetric(name, duration, type) {
            const metric = {
                name,
                duration,
                type,
                timestamp: Date.now()
            };
            
            if (!this.performanceMetrics.has(name)) {
                this.performanceMetrics.set(name, []);
            }
            
            this.performanceMetrics.get(name).push(metric);
            
            // Keep only last 100 measurements per metric
            if (this.performanceMetrics.get(name).length > 100) {
                this.performanceMetrics.get(name).shift();
            }
            
            // Alert on slow operations
            if (duration > 5000) { // 5 seconds
                console.warn(`⚠️ Slow operation detected: ${name} took ${duration}ms`);
            }
        }
        
        monitorLLMPerformance() {
            // Wrap LLM execution methods with performance monitoring
            if (window.EchoDispatcher) {
                const originalExecuteTask = window.EchoDispatcher.executeTask;
                window.EchoDispatcher.executeTask = async function(input, context) {
                    const startTime = performance.now();
                    try {
                        const result = await originalExecuteTask.call(this, input, context);
                        const duration = performance.now() - startTime;
                        
                        window.ProductionMonitor?.recordPerformanceMetric(
                            `llm_execution_${result.source}`, 
                            duration, 
                            'llm'
                        );
                        
                        return result;
                    } catch (error) {
                        const duration = performance.now() - startTime;
                        window.ProductionMonitor?.recordPerformanceMetric(
                            `llm_execution_error`, 
                            duration, 
                            'llm_error'
                        );
                        throw error;
                    }
                };
            }
        }
        
        monitorDatabasePerformance() {
            // Wrap database operations with performance monitoring
            if (window.AgentLeeDBHelpers) {
                const originalAddRecord = window.AgentLeeDBHelpers.addRecord;
                window.AgentLeeDBHelpers.addRecord = async function(storeName, record) {
                    const startTime = performance.now();
                    try {
                        const result = await originalAddRecord.call(this, storeName, record);
                        const duration = performance.now() - startTime;
                        
                        window.ProductionMonitor?.recordPerformanceMetric(
                            `db_add_${storeName}`, 
                            duration, 
                            'database'
                        );
                        
                        return result;
                    } catch (error) {
                        window.ProductionMonitor?.recordPerformanceMetric(
                            `db_add_error`, 
                            performance.now() - startTime, 
                            'database_error'
                        );
                        throw error;
                    }
                };
            }
        }
        
        recordMemoryUsage() {
            if ('memory' in performance) {
                const memInfo = {
                    used: performance.memory.usedJSHeapSize,
                    total: performance.memory.totalJSHeapSize,
                    limit: performance.memory.jsHeapSizeLimit,
                    timestamp: Date.now()
                };
                
                this.recordPerformanceMetric('memory_usage', memInfo.used, 'memory');
                
                // Alert on high memory usage (80% of limit)
                if (memInfo.used > memInfo.limit * 0.8) {
                    console.warn('⚠️ High memory usage detected:', memInfo);
                    this.logError('High Memory Usage', memInfo);
                }
            }
        }
        
        // ===== USER FEEDBACK SYSTEM =====
        
        initializeUserFeedback() {
            // Add feedback button to main interface
            this.createFeedbackButton();
            
            // Monitor user satisfaction indicators
            this.monitorUserSatisfaction();
        }
        
        createFeedbackButton() {
            // Wait for DOM to be ready
            if (document.readyState === 'loading') {
                document.addEventListener('DOMContentLoaded', () => this.createFeedbackButton());
                return;
            }

            // Check if button already exists
            if (document.getElementById('agent-lee-feedback-btn')) {
                return;
            }

            try {
                const feedbackButton = document.createElement('button');
                feedbackButton.id = 'agent-lee-feedback-btn';
                feedbackButton.innerHTML = '💬 Feedback';
                feedbackButton.style.cssText = `
                    position: fixed;
                    bottom: 20px;
                    right: 20px;
                    z-index: 999999;
                    background: linear-gradient(135deg, #6C47FF, #00E3FF);
                    border: none;
                    border-radius: 25px;
                    padding: 10px 20px;
                    color: white;
                    cursor: pointer;
                    font-family: 'Inter', sans-serif;
                    font-weight: 600;
                    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.3);
                    transition: all 0.3s ease;
                `;

                feedbackButton.addEventListener('click', () => {
                    this.showFeedbackDialog();
                });

                feedbackButton.addEventListener('mouseenter', () => {
                    feedbackButton.style.transform = 'translateY(-2px)';
                    feedbackButton.style.boxShadow = '0 6px 20px rgba(0, 0, 0, 0.4)';
                });

                feedbackButton.addEventListener('mouseleave', () => {
                    feedbackButton.style.transform = 'translateY(0)';
                    feedbackButton.style.boxShadow = '0 4px 15px rgba(0, 0, 0, 0.3)';
                });

                // Safely append to body
                if (document.body) {
                    document.body.appendChild(feedbackButton);
                    console.log('💬 Feedback button created');
                } else {
                    console.warn('💬 Document body not available for feedback button');
                }
            } catch (error) {
                console.error('❌ Failed to create feedback button:', error);
            }
        }
        
        showFeedbackDialog() {
            const dialog = document.createElement('div');
            dialog.style.cssText = `
                position: fixed;
                top: 50%;
                left: 50%;
                transform: translate(-50%, -50%);
                background: rgba(15, 23, 42, 0.95);
                border: 1px solid var(--primary-color);
                border-radius: 12px;
                padding: 30px;
                z-index: 1000000;
                backdrop-filter: blur(20px);
                box-shadow: 0 10px 30px rgba(0, 0, 0, 0.5);
                color: #e0e7ff;
                font-family: 'Inter', sans-serif;
                max-width: 500px;
                width: 90%;
            `;
            
            dialog.innerHTML = `
                <h3 style="color: #00f2ff; margin-bottom: 20px;">Agent Lee Feedback</h3>
                <textarea id="feedback-text" placeholder="Share your experience, report bugs, or suggest improvements..." 
                    style="width: 100%; height: 120px; background: rgba(0,0,0,0.3); border: 1px solid rgba(0,242,255,0.3); 
                    border-radius: 6px; padding: 10px; color: #e0e7ff; font-family: inherit; resize: vertical;"></textarea>
                <div style="margin-top: 15px; display: flex; gap: 10px; justify-content: flex-end;">
                    <button id="feedback-cancel" style="background: rgba(247,58,27,0.2); border: 1px solid #f73a1b; 
                        border-radius: 6px; padding: 8px 16px; color: #f73a1b; cursor: pointer;">Cancel</button>
                    <button id="feedback-submit" style="background: linear-gradient(135deg, #6C47FF, #00E3FF); 
                        border: none; border-radius: 6px; padding: 8px 16px; color: white; cursor: pointer;">Submit</button>
                </div>
            `;
            
            document.body.appendChild(dialog);
            
            // Event handlers
            dialog.querySelector('#feedback-cancel').addEventListener('click', () => {
                document.body.removeChild(dialog);
            });
            
            dialog.querySelector('#feedback-submit').addEventListener('click', () => {
                const feedback = dialog.querySelector('#feedback-text').value;
                if (feedback.trim()) {
                    this.submitFeedback(feedback);
                    document.body.removeChild(dialog);
                    this.showThankYouMessage();
                }
            });
            
            // Focus textarea
            dialog.querySelector('#feedback-text').focus();
        }
        
        async submitFeedback(feedback) {
            const feedbackData = {
                feedback_id: `feedback_${Date.now()}`,
                session_id: this.sessionId,
                feedback: feedback,
                timestamp: new Date().toISOString(),
                user_agent: navigator.userAgent,
                performance_summary: this.getPerformanceSummary(),
                error_count: this.errorCount
            };
            
            this.userFeedback.push(feedbackData);
            
            // Store in database
            try {
                if (window.AgentLeeDBHelpers) {
                    await window.AgentLeeDBHelpers.addRecord('telemetry', {
                        entry_id: feedbackData.feedback_id,
                        region: 'User Feedback',
                        event_type: 'user_feedback',
                        timestamp: feedbackData.timestamp,
                        metadata: feedbackData
                    });
                }
            } catch (error) {
                console.error('Failed to store feedback:', error);
            }
            
            console.log('📝 User feedback submitted:', feedbackData);
        }
        
        showThankYouMessage() {
            const toast = document.createElement('div');
            toast.textContent = '✅ Thank you for your feedback!';
            toast.style.cssText = `
                position: fixed;
                bottom: 80px;
                right: 20px;
                background: rgba(27, 247, 205, 0.9);
                color: rgba(0, 0, 0, 0.8);
                padding: 12px 20px;
                border-radius: 6px;
                z-index: 1000001;
                font-family: 'Inter', sans-serif;
                font-weight: 600;
                animation: slideIn 0.3s ease;
            `;
            
            document.body.appendChild(toast);
            
            setTimeout(() => {
                document.body.removeChild(toast);
            }, 3000);
        }

        monitorUserSatisfaction() {
            // This is a placeholder for more advanced user satisfaction monitoring
            // e.g., tracking rage clicks, repeated errors, negative sentiment in input
            console.log('🧐 User satisfaction monitoring (basic) active');
        }
        
        // ===== UTILITY METHODS =====
        
        getMemoryUsage() {
            if ('memory' in performance) {
                return {
                    used: performance.memory.usedJSHeapSize,
                    total: performance.memory.totalJSHeapSize,
                    limit: performance.memory.jsHeapSizeLimit
                };
            }
            return null;
        }
        
        getLLMStatus() {
            // Get current LLM status from the system
            return {
                echo_dispatcher_available: !!window.EchoDispatcher,
                loaded_guis: window.MicrofrontendLoader?.getLoadedGuis() || [],
                database_available: !!window.AgentLeeDB
            };
        }
        
        getPerformanceSummary() {
            const summary = {};
            for (const [name, metrics] of this.performanceMetrics) {
                if (metrics.length > 0) {
                    const durations = metrics.map(m => m.duration);
                    summary[name] = {
                        count: metrics.length,
                        avg: durations.reduce((a, b) => a + b, 0) / durations.length,
                        max: Math.max(...durations),
                        min: Math.min(...durations)
                    };
                }
            }
            return summary;
        }
        
        showCriticalErrorNotification() {
            const notification = document.createElement('div');
            notification.innerHTML = `
                <div style="display: flex; align-items: center; gap: 10px;">
                    <span style="font-size: 24px;">⚠️</span>
                    <div>
                        <div style="font-weight: 600;">Multiple errors detected</div>
                        <div style="font-size: 0.9rem; opacity: 0.8;">Agent Lee may need attention</div>
                    </div>
                </div>
            `;
            notification.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                background: rgba(247, 58, 27, 0.9);
                color: white;
                padding: 15px 20px;
                border-radius: 8px;
                z-index: 1000002;
                font-family: 'Inter', sans-serif;
                box-shadow: 0 4px 15px rgba(0, 0, 0, 0.3);
            `;
            
            document.body.appendChild(notification);
            
            setTimeout(() => {
                if (document.body.contains(notification)) {
                    document.body.removeChild(notification);
                }
            }, 10000);
        }
        
        // ===== PUBLIC API =====
        
        getSessionReport() {
            return {
                session_id: this.sessionId,
                uptime: Date.now() - this.startTime,
                error_count: this.errorCount,
                performance_summary: this.getPerformanceSummary(),
                memory_usage: this.getMemoryUsage(),
                feedback_count: this.userFeedback.length,
                llm_status: this.getLLMStatus()
            };
        }
        
        exportLogs() {
            const report = this.getSessionReport();
            const blob = new Blob([JSON.stringify(report, null, 2)], { type: 'application/json' });
            const url = URL.createObjectURL(blob);
            
            const a = document.createElement('a');
            a.href = url;
            a.download = `agent_lee_session_${this.sessionId}.json`;
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            URL.revokeObjectURL(url);
        }
    }

    // Initialize global production monitor
    window.ProductionMonitor = new AgentLeeProductionMonitor();
})();
// --- END OF FILE production_monitoring.js ---

// --- START OF FILE real_llm_manager.js ---
// REAL LLM Manager for Agent Lee - Loads Actual Models
(function() {
    console.log('🧠 Loading REAL LLM Manager...');

    class RealLLMManager {
        constructor() {
            this.models = new Map();
            this.loadingStatus = new Map();
            this.modelPaths = {
                azr: 'D:/THEBESTAGENTLEE23/llama_models/andrewzh_Absolute_Zero_Reasoner-Coder-3b-Q4_K_M.gguf',
                phi3: 'D:/THEBESTAGENTLEE23/llama_models/fine_tuned_phi3/',
                gemini: 'api', // Gemini API
                webllm: 'cdn' // WebLLM from CDN
            };
            
            console.log('🧠 Real LLM Manager initialized with model paths:', this.modelPaths);
        }

        async initializeAZR() {
            console.log('🔄 Initializing AZR model...');
            try {
                // AZR is handled by the Python bridge
                if (window.AZRBridgeClient) {
                    await window.AZRBridgeClient.connect();
                    this.models.set('azr', { 
                        type: 'bridge', 
                        status: 'loaded',
                        path: this.modelPaths.azr 
                    });
                    console.log('✅ AZR model connected via bridge');
                    return true;
                } else {
                    throw new Error('AZR Bridge Client not available');
                }
            } catch (error) {
                console.error('❌ AZR initialization failed:', error);
                this.loadingStatus.set('azr', { loaded: false, error: error.message });
                return false;
            }
        }

        async initializePHI3() {
            console.log('🔄 Initializing PHI-3 model...');
            try {
                // Load PHI-3 via Transformers.js
                if (!window.transformers) {
                    await window.loadTransformers();
                }
                
                if (window.transformers) {
                    // Create a text generation pipeline for PHI-3
                    const phi3Pipeline = await window.transformers.pipeline(
                        'text-generation',
                        'microsoft/Phi-3-mini-4k-instruct',
                        { 
                            device: 'webgpu',
                            dtype: 'fp16'
                        }
                    );
                    
                    this.models.set('phi3', {
                        type: 'transformers',
                        pipeline: phi3Pipeline,
                        status: 'loaded',
                        path: this.modelPaths.phi3
                    });
                    
                    console.log('✅ PHI-3 model loaded via Transformers.js');
                    return true;
                } else {
                    throw new Error('Transformers.js not available');
                }
            } catch (error) {
                console.error('❌ PHI-3 initialization failed:', error);
                this.loadingStatus.set('phi3', { loaded: false, error: error.message });
                return false;
            }
        }

        async initializeGemini(apiKey = null) {
            console.log('🔄 Initializing Gemini API...');
            try {
                // Use provided API key or try to get from environment
                // For browser environment, process.env.GEMINI_API_KEY won't work unless set by a bundler.
                // It's better to pass it explicitly or have a configuration mechanism.
                const key = apiKey || (typeof process !== 'undefined' && process.env && process.env.GEMINI_API_KEY) || 'demo_key';
                
                if (!key || key === 'demo_key') {
                    console.warn('⚠️ No Gemini API key provided, using mock responses');
                    this.models.set('gemini', {
                        type: 'mock',
                        status: 'loaded',
                        apiKey: null
                    });
                    return true;
                }

                // Initialize Gemini API
                const geminiAPI = {
                    apiKey: key,
                    baseURL: 'https://generativelanguage.googleapis.com/v1beta/models/gemini-pro:generateContent',
                    
                    async generate(prompt, options = {}) {
                        const response = await fetch(`${this.baseURL}?key=${this.apiKey}`, {
                            method: 'POST',
                            headers: { 'Content-Type': 'application/json' },
                            body: JSON.stringify({
                                contents: [{ parts: [{ text: prompt }] }],
                                generationConfig: {
                                    temperature: options.temperature || 0.7,
                                    maxOutputTokens: options.maxTokens || 200
                                }
                            })
                        });
                        
                        const data = await response.json();
                        if (data.candidates && data.candidates[0] && data.candidates[0].content && data.candidates[0].content.parts && data.candidates[0].content.parts[0]) {
                           return data.candidates[0].content.parts[0].text;
                        } else {
                           console.error("Unexpected Gemini API response structure:", data);
                           throw new Error("Invalid Gemini API response structure");
                        }
                    }
                };

                this.models.set('gemini', {
                    type: 'api',
                    api: geminiAPI,
                    status: 'loaded',
                    apiKey: key
                });

                console.log('✅ Gemini API initialized');
                return true;
            } catch (error) {
                console.error('❌ Gemini initialization failed:', error);
                this.loadingStatus.set('gemini', { loaded: false, error: error.message });
                return false;
            }
        }

        async initializeWebLLM() {
            console.log('🔄 Initializing WebLLM...');
            try {
                if (!window.webllmLoaded) {
                    await window.loadWebLLM();
                    await new Promise(resolve => setTimeout(resolve, 2000)); // Wait for load
                }

                if (window.webllm || window.WebLLM) {
                    const webllmModule = window.webllm || window.WebLLM; // WebLLM might be on window or window.WebLLM
                    
                    // Initialize WebLLM engine
                    const engine = await webllmModule.CreateMLCEngine('Llama-2-7b-chat-hf-q4f16_1');
                    
                    this.models.set('webllm', {
                        type: 'webllm',
                        engine: engine,
                        status: 'loaded'
                    });

                    console.log('✅ WebLLM initialized');
                    return true;
                } else {
                    throw new Error('WebLLM not available');
                }
            } catch (error) {
                console.error('❌ WebLLM initialization failed:', error);
                this.loadingStatus.set('webllm', { loaded: false, error: error.message });
                return false;
            }
        }

        async generateWithModel(modelName, prompt, options = {}) {
            const model = this.models.get(modelName);
            if (!model) {
                throw new Error(`Model ${modelName} not loaded`);
            }

            console.log(`🧠 Generating with ${modelName}:`, prompt.substring(0, 50) + '...');

            try {
                switch (model.type) {
                    case 'bridge':
                        // AZR via bridge
                        const azrResponse = await window.AZRBridgeClient.sendTask({
                            type: 'azr_task',
                            prompt: prompt,
                            request_id: `gen_${Date.now()}`
                        });
                        return azrResponse.result;

                    case 'transformers':
                        // PHI-3 via Transformers.js
                        const phi3Result = await model.pipeline(prompt, {
                            max_new_tokens: options.maxTokens || 150,
                            temperature: options.temperature || 0.3,
                            do_sample: true
                        });
                        return phi3Result[0].generated_text;

                    case 'api':
                        // Gemini API
                        return await model.api.generate(prompt, options);

                    case 'webllm':
                        // WebLLM
                        const webllmResult = await model.engine.chat.completions.create({
                            messages: [{ role: 'user', content: prompt }],
                            temperature: options.temperature || 0.7,
                            max_tokens: options.maxTokens || 150
                        });
                        return webllmResult.choices[0].message.content;

                    case 'mock':
                        // Mock response for testing
                        return `Mock ${modelName} response to: "${prompt.substring(0, 30)}..."`;

                    default:
                        throw new Error(`Unknown model type: ${model.type}`);
                }
            } catch (error) {
                console.error(`❌ Generation failed for ${modelName}:`, error);
                throw error;
            }
        }

        async initializeAllModels() {
            console.log('🚀 Initializing all LLM models...');
            
            const results = {
                azr: await this.initializeAZR(),
                phi3: await this.initializePHI3(),
                gemini: await this.initializeGemini(),
                webllm: await this.initializeWebLLM()
            };

            const successCount = Object.values(results).filter(Boolean).length;
            console.log(`✅ LLM initialization complete: ${successCount}/4 models loaded`);
            
            return results;
        }

        getModelStatus() {
            const status = {};
            for (const [name, model] of this.models) {
                status[name] = {
                    loaded: true,
                    type: model.type,
                    status: model.status,
                    path: model.path || 'N/A'
                };
            }
            
            // Add failed models
            for (const [name, loadStatus] of this.loadingStatus) {
                if (!status[name]) {
                    status[name] = loadStatus;
                }
            }
            
            return status;
        }

        isModelLoaded(modelName) {
            return this.models.has(modelName) && this.models.get(modelName).status === 'loaded';
        }
    }

    // Initialize the REAL LLM Manager
    window.RealLLMManager = new RealLLMManager();
    console.log('✅ Real LLM Manager created and available globally');
})();
// --- END OF FILE real_llm_manager.js ---

// --- START OF FILE working_echo_dispatcher.js ---
// Working Echo Dispatcher - Uses Real LLMs
(function() {
    console.log('🎯 Loading Working Echo Dispatcher...');

    class WorkingEchoDispatcher {
        constructor() {
            this.llmManager = null;
            this.azrClient = null;
            this.isInitialized = false;
            this.routingRules = {
                azr: {
                    keywords: ['analyze', 'complex', 'reasoning', 'problem', 'strategy', 'plan', 'evaluate', 'compare', 'assess'],
                    minLength: 30,
                    confidence: 0.8
                },
                phi3: {
                    keywords: ['validate', 'check', 'quick', 'simple', 'yes', 'no', 'format', 'verify'],
                    maxLength: 50,
                    confidence: 0.7
                },
                gemini: {
                    keywords: ['creative', 'generate', 'story', 'ui', 'design', 'imagine', 'create'],
                    confidence: 0.6
                },
                webllm: {
                    keywords: ['summarize', 'explain', 'chat', 'general', 'help', 'what', 'how'],
                    confidence: 0.5
                }
            };
            
            console.log('🎯 Working Echo Dispatcher initialized');
            this.initialize();
        }

        async initialize() {
            try {
                console.log('🔄 Initializing Working Echo Dispatcher...');
                
                // Initialize LLM Manager
                if (window.RealLLMManager) {
                    this.llmManager = window.RealLLMManager;
                    console.log('✅ Real LLM Manager connected');
                } else {
                    console.warn('⚠️ Real LLM Manager not available');
                }

                // Initialize AZR Bridge Client
                if (window.AZRBridgeClient) {
                    this.azrClient = window.AZRBridgeClient;
                    console.log('✅ AZR Bridge Client connected');
                } else {
                    console.warn('⚠️ AZR Bridge Client not available');
                }

                // Initialize models
                if (this.llmManager) {
                    await this.llmManager.initializeAllModels();
                }

                this.isInitialized = true;
                console.log('✅ Working Echo Dispatcher fully initialized');
                
            } catch (error) {
                console.error('❌ Working Echo Dispatcher initialization failed:', error);
            }
        }

        routeTask(input) {
            const normalizedInput = input.toLowerCase();
            const inputLength = input.length;
            
            let bestMatch = {
                target: 'webllm', // Default fallback
                confidence: 0.1,
                reasoning: 'default fallback'
            };

            // Check each LLM's routing rules
            for (const [llmName, rules] of Object.entries(this.routingRules)) {
                let confidence = 0;
                let matchedKeywords = [];

                // Keyword matching
                if (rules.keywords) {
                    for (const keyword of rules.keywords) {
                        if (normalizedInput.includes(keyword)) {
                            confidence += 0.3; // Base score for keyword match
                            matchedKeywords.push(keyword);
                        }
                    }
                }


                // Length-based scoring
                if (rules.minLength && inputLength >= rules.minLength) {
                    confidence += 0.2;
                }
                if (rules.maxLength && inputLength <= rules.maxLength) {
                    confidence += 0.2;
                }

                // Base confidence for the rule itself
                confidence += rules.confidence * 0.3; // Weight base confidence

                // Bonus for multiple keyword matches
                if (matchedKeywords.length > 1) {
                    confidence += 0.1 * matchedKeywords.length;
                }
                
                // Ensure model is loaded for this target
                if (this.llmManager && !this.llmManager.isModelLoaded(llmName)) {
                     // If model not loaded, significantly reduce confidence or skip
                     // For simplicity, let's just reduce confidence. A more robust system might reroute.
                     // confidence *= 0.1; // Drastically reduce confidence if model not ready
                     continue; // Or, skip if model is not loaded
                }


                // Update best match
                if (confidence > bestMatch.confidence) {
                    bestMatch = {
                        target: llmName,
                        confidence: Math.min(confidence, 0.95), // Cap confidence
                        reasoning: matchedKeywords.length > 0 
                            ? `Matched keywords: ${matchedKeywords.join(', ')}`
                            : `Pattern match for ${llmName}`
                    };
                }
            }

            console.log(`🎯 Routing decision: "${input.substring(0, 30)}..." → ${bestMatch.target} (${bestMatch.confidence.toFixed(2)})`);
            return bestMatch;
        }

        async executeTask(input, context = {}) {
            if (!this.isInitialized) {
                console.log('⏳ Echo Dispatcher not initialized, waiting...');
                await this.initialize(); // Ensure initialization
            }
            if(!this.isInitialized) { // Check again after await
                return {
                    success: false,
                    error: "Dispatcher failed to initialize",
                    source: "dispatcher_init_error",
                    timestamp: new Date().toISOString()
                };
            }


            const routing = this.routeTask(input);
            console.log(`🎯 Executing task with ${routing.target}:`, input.substring(0, 50) + '...');

            try {
                let result;

                switch (routing.target) {
                    case 'azr':
                        result = await this.executeWithAZR(input, context);
                        break;
                    case 'phi3':
                        result = await this.executeWithPHI3(input, context);
                        break;
                    case 'gemini':
                        result = await this.executeWithGemini(input, context);
                        break;
                    case 'webllm':
                        result = await this.executeWithWebLLM(input, context);
                        break;
                    default:
                        // Fallback if routing target is somehow unknown or model not loaded
                        console.warn(`Unknown or unavailable target: ${routing.target}, attempting WebLLM fallback.`);
                        result = await this.executeWithWebLLM(input, context);
                        routing.target = 'webllm_fallback_unknown'; // Mark as fallback
                }

                return {
                    success: true,
                    result: result,
                    source: routing.target,
                    routing: routing,
                    timestamp: new Date().toISOString()
                };

            } catch (error) {
                console.error(`❌ Task execution failed with ${routing.target}:`, error);
                
                // Try fallback to AZR or WebLLM if primary failed
                let fallbackAttempted = false;
                if (routing.target !== 'azr' && this.llmManager && this.llmManager.isModelLoaded('azr')) {
                    try {
                        console.log('🔄 Falling back to AZR...');
                        fallbackAttempted = true;
                        const fallbackResult = await this.executeWithAZR(input, context);
                        return {
                            success: true,
                            result: fallbackResult,
                            source: 'azr_fallback',
                            routing: routing,
                            timestamp: new Date().toISOString()
                        };
                    } catch (fallbackError) {
                        console.error('❌ Fallback to AZR also failed:', fallbackError);
                    }
                }
                
                if (!fallbackAttempted && routing.target !== 'webllm' && this.llmManager && this.llmManager.isModelLoaded('webllm')) {
                     try {
                        console.log('🔄 Falling back to WebLLM...');
                        const fallbackResult = await this.executeWithWebLLM(input, context);
                        return {
                            success: true,
                            result: fallbackResult,
                            source: 'webllm_fallback',
                            routing: routing,
                            timestamp: new Date().toISOString()
                        };
                    } catch (fallbackError) {
                        console.error('❌ Fallback to WebLLM also failed:', fallbackError);
                    }
                }


                return {
                    success: false,
                    error: error.message,
                    source: routing.target,
                    routing: routing,
                    timestamp: new Date().toISOString()
                };
            }
        }

        async executeWithAZR(input, context) {
            if (!this.azrClient) {
                throw new Error('AZR Bridge Client not available');
            }

            if (!this.azrClient.isConnected) {
                await this.azrClient.connect();
            }

            const response = await this.azrClient.sendTask({
                type: 'azr_task',
                prompt: input,
                context: context,
                request_id: `echo_${Date.now()}`
            });

            return response.result;
        }

        async executeWithPHI3(input, context) {
            if (!this.llmManager || !this.llmManager.isModelLoaded('phi3')) {
                throw new Error('PHI-3 model not available');
            }

            return await this.llmManager.generateWithModel('phi3', input, {
                maxTokens: 100,
                temperature: 0.3
            });
        }

        async executeWithGemini(input, context) {
            if (!this.llmManager || !this.llmManager.isModelLoaded('gemini')) {
                throw new Error('Gemini model not available');
            }

            return await this.llmManager.generateWithModel('gemini', input, {
                maxTokens: 200,
                temperature: 0.7
            });
        }

        async executeWithWebLLM(input, context) {
            if (!this.llmManager || !this.llmManager.isModelLoaded('webllm')) {
                throw new Error('WebLLM model not available');
            }

            return await this.llmManager.generateWithModel('webllm', input, {
                maxTokens: 150,
                temperature: 0.6
            });
        }

        getSystemStatus() {
            return {
                initialized: this.isInitialized,
                azrClient: !!this.azrClient,
                azrConnected: this.azrClient?.isConnected || false,
                llmManager: !!this.llmManager,
                modelStatus: this.llmManager?.getModelStatus() || {},
                routingRules: this.routingRules
            };
        }

        async testAllModels() {
            const testPrompt = "Hello, this is a test message.";
            const results = {};

            for (const modelName of ['azr', 'phi3', 'gemini', 'webllm']) {
                if(this.llmManager && this.llmManager.isModelLoaded(modelName)){
                    try {
                        console.log(`🧪 Testing ${modelName}...`);
                        const result = await this.executeTask(`Test ${modelName}: ${testPrompt}`); // Use executeTask for routing
                        results[modelName] = {
                            success: result.success,
                            routedTo: result.source,
                            response: result.result?.substring(0, 100) + '...',
                            error: result.error
                        };
                    } catch (error) {
                        results[modelName] = {
                            success: false,
                            error: error.message
                        };
                    }
                } else {
                     results[modelName] = {
                        success: false,
                        error: `Model ${modelName} not loaded.`
                    };
                }
            }
            return results;
        }
    }

    // Initialize the Working Echo Dispatcher
    window.WorkingEchoDispatcher = new WorkingEchoDispatcher();
    // Expose EchoDispatcher as well if other parts of the code expect it
    window.EchoDispatcher = window.WorkingEchoDispatcher; 
    console.log('✅ Working Echo Dispatcher created and available globally as window.WorkingEchoDispatcher and window.EchoDispatcher');
})();
// --- END OF FILE working_echo_dispatcher.js ---

// --- START OF FILE cognitive-matrix.js ---
// This script is self-contained and uses its own internal 'llmAgents' and 'schemaFiles'.
// It might define its own globals or expect some like 'addActivityItem'.
(function() {
    /**
     * Agent Lee Cognitive Matrix - Production LLM System
     * Complete JavaScript implementation for the cognitive schema router
     */

    // LLM Agent Definitions (internal to cognitive-matrix.js)
    const llmAgents = {
        azr: {
            name: "AZR",
            role: "Central Reasoner / Coordinator",
            color: "#00d4ff",
            avatar: "🧠", // Placeholder, will be replaced by image path
            status: "active",
            description: "Central intelligence hub that coordinates all LLM agents and manages schema file routing.",
            tools: [
                { name: "Task Router", description: "Routes tasks to appropriate LLMs based on capability" },
                { name: "Schema Manager", description: "Manages access to all schema and JSON files" },
                { name: "Flow Controller", description: "Controls data flow between LLMs and files" },
                { name: "System Monitor", description: "Monitors overall system health and performance" }
            ],
            currentTasks: [
                { task: "Routing task to PHI-3", status: "active", progress: 75 },
                { task: "Schema validation check", status: "queued", progress: 0 },
                { task: "System health monitoring", status: "active", progress: 100 }
            ],
            activeFiles: [
                "Agent_Coordination_Schema.csv", // Note: Original file had .csv, JSON version has .json
                "Schema_Overview_for_AgentLee_DB.csv",
                "worker_status.json",
                "task_progress.json"
            ]
        },
        
        phi3: {
            name: "PHI-3",
            role: "Task Proposal & Planning",
            color: "#2196F3",
            avatar: "🧠", // Placeholder
            status: "active",
            description: "Specialized in rapid task analysis and creating structured proposals for complex problems.",
            tools: [
                { name: "Task Analyzer", description: "Breaks down complex tasks into manageable components" },
                { name: "Framework Generator", description: "Creates structural frameworks for problem-solving" },
                { name: "Priority Assessor", description: "Evaluates and assigns priorities to tasks" },
                { name: "Schema Validator", description: "Validates proposed structures against existing schemas" }
            ],
            currentTasks: [
                { task: "Analyzing user request", status: "active", progress: 85 },
                { task: "Creating task framework", status: "queued", progress: 0 },
                { task: "Schema integration check", status: "pending", progress: 0 }
            ],
            activeFiles: [
                "Schema_Overview_for_AgentLee_DB.csv",
                "Agent_Lee___System-Level_Behavior_Schema.csv",
                "task_progress.json"
            ]
        },
        
        gemini: {
            name: "GEMINI",
            role: "Explanation, Search & Summarization",
            color: "#9C27B0",
            avatar: "💎", // Placeholder
            status: "idle",
            description: "Provides comprehensive explanations, searches knowledge bases, and creates detailed summaries.",
            tools: [
                { name: "Knowledge Search", description: "Searches through vast knowledge databases" },
                { name: "Explanation Engine", description: "Generates clear, detailed explanations" },
                { name: "Summary Generator", description: "Creates concise summaries from complex data" },
                { name: "Context Analyzer", description: "Analyzes context to provide relevant information" }
            ],
            currentTasks: [
                { task: "Standby mode", status: "idle", progress: 0 }
            ],
            activeFiles: [
                "cognitive_events.json",
                "Agent_Lee__Deep_Schema_Table.csv"
            ]
        },
        
        echo: {
            name: "ECHO",
            role: "Memory Monitoring & Diagnostics",
            color: "#00BCD4",
            avatar: "📡", // Placeholder
            status: "active",
            description: "Monitors system memory, performs diagnostics, and maintains system health metrics.",
            tools: [
                { name: "Memory Monitor", description: "Tracks memory usage across all LLMs" },
                { name: "Diagnostic Scanner", description: "Performs system health diagnostics" },
                { name: "Performance Tracker", description: "Monitors and logs performance metrics" },
                { name: "Alert System", description: "Sends alerts for system anomalies" }
            ],
            currentTasks: [
                { task: "Memory usage monitoring", status: "active", progress: 100 },
                { task: "System diagnostics", status: "active", progress: 60 },
                { task: "Performance logging", status: "active", progress: 100 }
            ],
            activeFiles: [
                "worker_status.json",
                "cognitive_events.json",
                "crosslink_sockets.json"
            ]
        },
        
        qwen: {
            name: "QWEN",
            role: "Result Verifier & Validator",
            color: "#4CAF50",
            avatar: "✅", // Placeholder
            status: "active",
            description: "Validates results, verifies logical consistency, and ensures quality control across all outputs.",
            tools: [
                { name: "Logic Verifier", description: "Checks logical consistency of results" },
                { name: "Quality Controller", description: "Ensures outputs meet quality standards" },
                { name: "Accuracy Checker", description: "Validates accuracy of information" },
                { name: "Consistency Analyzer", description: "Analyzes consistency across different outputs" }
            ],
            currentTasks: [
                { task: "Verifying PHI-3 output", status: "active", progress: 45 },
                { task: "Quality check queue", status: "queued", progress: 0 }
            ],
            activeFiles: [
                "Agent_Coordination_Schema.csv",
                "Agent_Lee__Deep_Schema_Table.csv"
            ]
        },
        
        llama: {
            name: "LLAMA",
            role: "Longform Generation / Fallback",
            color: "#FF9800",
            avatar: "🦙", // Placeholder
            status: "sleeping",
            description: "Handles longform content generation and serves as fallback for complex reasoning tasks.",
            tools: [
                { name: "Content Generator", description: "Generates long-form content and documentation" },
                { name: "Fallback Reasoner", description: "Handles complex tasks when other LLMs are unavailable" },
                { name: "Context Synthesizer", description: "Synthesizes information from multiple sources" },
                { name: "Narrative Builder", description: "Creates coherent narratives from data points" }
            ],
            currentTasks: [
                { task: "Standby mode", status: "sleeping", progress: 0 }
            ],
            activeFiles: [
                "llms.json"
            ]
        }
    };

    // Schema Files Database (internal to cognitive-matrix.js)
    // Note: file extensions here might differ from the JSON variables defined earlier.
    // This script will use these internal definitions.
    const schemaFiles = {
        "Schema_Overview_for_AgentLee_DB.csv": {
            purpose: "Task & priority data",
            status: "active",
            size: "45KB",
            lastModified: "2 min ago",
            connectedLLMs: ["azr", "phi3"]
        },
        "task_progress.json": {
            purpose: "Real-time task completions",
            status: "updating",
            size: "12KB",
            lastModified: "30s ago",
            connectedLLMs: ["azr", "phi3"]
        },
        "worker_status.json": { // Corresponds to worker_status_fixed.json
            purpose: "Worker assignments",
            status: "active",
            size: "8KB",
            lastModified: "1 min ago",
            connectedLLMs: ["azr", "echo"]
        },
        "Agent_Coordination_Schema.csv": { // Corresponds to Agent_Coordination_Schema.json
            purpose: "Inter-agent relationship map",
            status: "active",
            size: "23KB",
            lastModified: "5 min ago",
            connectedLLMs: ["azr", "qwen"]
        },
        "Agent_Lee___System-Level_Behavior_Schema.csv": { // Corresponds to Agent_Lee___System-Level_Behavior_Schema.json
            purpose: "Macro logic triggers",
            status: "loaded",
            size: "67KB",
            lastModified: "10 min ago",
            connectedLLMs: ["phi3"]
        },
        "Agent_Lee__Deep_Schema_Table.csv": { // Corresponds to Agent_Lee__Deep_Schema_Table.json
            purpose: "Micro execution maps",
            status: "active",
            size: "134KB",
            lastModified: "3 min ago",
            connectedLLMs: ["gemini", "qwen"]
        },
        "cognitive_events.json": { // Corresponds to cognitive_events_fixed.json
            purpose: "Logged LLM activity & memory",
            status: "error", // Example status
            size: "256KB",
            lastModified: "15 min ago",
            connectedLLMs: ["echo", "gemini"]
        },
        "crosslink_sockets.json": { // This JSON was not provided separately
            purpose: "Communication endpoints",
            status: "error",
            size: "4KB",
            lastModified: "20 min ago",
            connectedLLMs: ["echo"]
        },
        "llms.json": { // This JSON was not provided separately (AgentLee.DetailedLLMAgentsData is from llm_agents.js)
            purpose: "All LLM definitions and capabilities",
            status: "updating",
            size: "89KB",
            lastModified: "5s ago",
            connectedLLMs: ["llama"]
        }
    };

    // Global state management for cognitive-matrix
    let currentActiveCard = null;
    let systemMetrics = {
        activeLLMs: 4,
        totalLLMs: 6, // Adjusted to match the number of agents defined here
        activeSchemas: 5,
        totalSchemas: Object.keys(schemaFiles).length,
        flowConnections: 12,
        totalConnections: 15,
        tasksQueued: 6,
        processRate: 2.1,
        previousActiveLLMs: 3,
        previousActiveSchemas: 5,
        previousTasks: 8,
        previousRate: 1.8
    };

    // Initialize the system
    document.addEventListener('DOMContentLoaded', function() {
        initializeSystem();
        startSystemMonitoring();
    });

    function initializeSystem() {
        console.log('🚀 Initializing Agent Lee Cognitive Matrix...');
        updateSystemStatus();
        createAgentCards();
        // animateFlowLines(); // Removed as per original code comment
        
        document.addEventListener('keydown', handleKeyboardShortcuts);
        
        console.log('✅ Agent Lee Cognitive Matrix initialized successfully');
    }

    function createAgentCards() {
        const container = document.getElementById('agentCardsContainer');
        if (!container) {
            console.warn("Cognitive Matrix: 'agentCardsContainer' not found. Cards will not be created.");
            return;
        }
        container.innerHTML = ''; // Clear existing cards
        
        // Avatar mapping for each agent - replace with actual paths or keep as placeholders
        // Using generic placeholders as actual image files are not provided
        const avatarMap = {
            'azr': 'img/azr_avatar.png', // Example path, ensure these images exist
            'phi3': 'img/phi3_avatar.png',
            'gemini': 'img/gemini_avatar.png',
            'echo': 'img/echo_avatar.png',
            'qwen': 'img/qwen_avatar.png',
            'llama': 'img/llama_avatar.png'
        };
        
        Object.entries(llmAgents).forEach(([key, agent]) => {
            const card = document.createElement('div');
            card.className = 'agent-card'; // Ensure CSS class "agent-card" is defined
            card.id = `card-${key}`;
            card.style.borderColor = agent.color;
            card.style.color = agent.color;
            card.style.display = 'none'; // Initially hidden
            
            // Default to text avatar if image path is a placeholder or image fails to load
            let avatarHTML = `<div class="card-avatar-text" style="border-color: ${agent.color};">${agent.avatar}</div>`;
            if (avatarMap[key] && !avatarMap[key].includes("Placeholder")) { // Check if it's not a generic placeholder
                 avatarHTML = `<div class="card-avatar" style="border-color: ${agent.color}; background-image: url('${avatarMap[key]}'); background-size: cover; background-position: center;"></div>`;
            }


            card.innerHTML = `
                <button class="close-btn" onclick="AgentLeeCognitiveMatrix.hideAgentCard('${key}')">&times;</button>
                
                <div class="card-header">
                    ${avatarHTML}
                    <div class="card-info">
                        <h2>${agent.name}</h2>
                        <h3>${agent.role}</h3>
                        <div style="display: flex; align-items: center; gap: 10px;">
                            <span style="font-size: 12px;">Status:</span>
                            <div class="status-ring ${agent.status}" style="position: relative; top: 0; right: 0;"></div>
                            <span style="font-size: 12px; text-transform: capitalize;">${agent.status}</span>
                        </div>
                    </div>
                </div>
                
                <div style="margin-bottom: 20px;">
                    <h4 style="margin-bottom: 10px;">📋 Description</h4>
                    <p style="opacity: 0.9; line-height: 1.5;">${agent.description}</p>
                </div>
                
                <div style="margin-bottom: 20px;">
                    <h4 style="margin-bottom: 10px;">⚙️ Tools & Capabilities</h4>
                    <div class="tool-grid">
                        ${agent.tools.map(tool => `
                            <div class="tool-item">
                                <h5 style="margin-bottom: 5px;">${tool.name}</h5>
                                <p style="font-size: 12px; opacity: 0.8;">${tool.description}</p>
                            </div>
                        `).join('')}
                    </div>
                </div>
                
                <div style="margin-bottom: 20px;">
                    <h4 style="margin-bottom: 10px;">📝 Current Tasks</h4>
                    <div class="task-queue">
                        ${agent.currentTasks.map(task => `
                            <div class="task-item">
                                <div>
                                    <div style="font-weight: bold; margin-bottom: 3px;">${task.task}</div>
                                    <div style="font-size: 11px; opacity: 0.7; text-transform: capitalize;">Status: ${task.status}</div>
                                </div>
                                <div style="width: 100px;">
                                    <div class="progress-bar">
                                        <div class="progress-fill" style="width: ${task.progress}%;"></div>
                                    </div>
                                    <div style="font-size: 10px; text-align: right;">${task.progress}%</div>
                                </div>
                            </div>
                        `).join('')}
                    </div>
                </div>
                
                <div style="margin-bottom: 20px;">
                    <h4 style="margin-bottom: 10px;">📁 Active Files</h4>
                    <div class="file-list">
                        ${agent.activeFiles.map(file => `
                            <div class="file-tag" onclick="AgentLeeCognitiveMatrix.highlightSchemaFile('${file}')">${file}</div>
                        `).join('')}
                    </div>
                </div>
                
                <div style="text-align: center; margin-top: 20px;">
                    <button onclick="AgentLeeCognitiveMatrix.runAgentTask('${key}')" 
                            style="background: ${agent.color}; color: white; border: none; padding: 10px 20px; 
                                   border-radius: 8px; cursor: pointer; margin: 0 5px; font-size: 14px;">
                        Run Task
                    </button>
                    <button onclick="AgentLeeCognitiveMatrix.toggleAgentStatus('${key}')" 
                            style="background: rgba(255,255,255,0.1); color: white; border: 1px solid ${agent.color}; 
                                   padding: 10px 20px; border-radius: 8px; cursor: pointer; margin: 0 5px; font-size: 14px;">
                        Toggle Status
                    </button>
                    <button onclick="AgentLeeCognitiveMatrix.viewAgentLogs('${key}')" 
                            style="background: rgba(255,255,255,0.1); color: white; border: 1px solid ${agent.color}; 
                                   padding: 10px 20px; border-radius: 8px; cursor: pointer; margin: 0 5px; font-size: 14px;">
                        View Logs
                    </button>
                </div>
            `;
            
            container.appendChild(card);
        });
    }

    function showAgentCard(agentKey) {
        if (currentActiveCard) {
            hideAgentCard(currentActiveCard);
        }
        
        const card = document.getElementById(`card-${agentKey}`);
        if (card) {
            card.style.display = 'block';
            card.style.animation = 'cardAppear 0.3s ease-out';
            currentActiveCard = agentKey;
            highlightAgent(agentKey);
            console.log(`📖 Opened ${llmAgents[agentKey].name} agent card`);
        }
    }

    function hideAgentCard(agentKey) {
        const card = document.getElementById(`card-${agentKey}`);
        if (card) {
            card.style.animation = 'cardAppear 0.3s ease-out reverse';
            setTimeout(() => {
                card.style.display = 'none';
            }, 290); // slightly less than animation duration
            
            if (currentActiveCard === agentKey) {
                currentActiveCard = null;
            }
            removeAgentHighlight(agentKey);
        }
    }

    function highlightAgent(agentKey) {
        const agentElement = document.querySelector(`.agent-representation.${agentKey}`); // Assuming agent visuals have class like .azr, .phi3
        if (agentElement) {
            agentElement.style.transform = 'scale(1.2)';
            agentElement.style.zIndex = '100';
            agentElement.style.boxShadow = `0 0 50px ${llmAgents[agentKey].color}`;
        }
    }

    function removeAgentHighlight(agentKey) {
        const agentElement = document.querySelector(`.agent-representation.${agentKey}`);
        if (agentElement) {
            agentElement.style.transform = '';
            agentElement.style.zIndex = '';
            agentElement.style.boxShadow = '';
        }
    }

    function runAgentTask(agentKey) {
        const agent = llmAgents[agentKey];
        console.log(`🔄 Running task for ${agent.name}...`);
        
        if (typeof addActivityItem === 'function') { // Check if global addActivityItem exists
            addActivityItem(`🔄 ${agent.name} started new task`);
        }
        
        const taskNames = ["Processing new request", "Analyzing data structure", "Validating output quality", "Generating response", "Updating schema files", "Optimizing neural pathways", "Coordinating with other agents", "Processing cognitive patterns"];
        const randomTask = taskNames[Math.floor(Math.random() * taskNames.length)];
        
        agent.currentTasks.unshift({ task: randomTask, status: "active", progress: 0 });
        
        const agentElement = document.querySelector(`.agent-representation.${agentKey} .progress-fill`); // More specific selector
        
        if (currentActiveCard === agentKey) { // Re-render card content if open
             const card = document.getElementById(`card-${agentKey}`);
             // This is a simplified update. A more robust solution would re-render parts of the card's innerHTML.
             // For now, we'll just log and assume the progress bar on the main UI might update.
        }
        
        let progress = 0;
        const progressInterval = setInterval(() => {
            progress += Math.random() * 12 + 3;
            if (progress >= 100) {
                progress = 100;
                clearInterval(progressInterval);
                agent.currentTasks[0].status = "completed";
                if (typeof addActivityItem === 'function') {
                    addActivityItem(`✅ ${agent.name} completed: ${randomTask}`);
                }
                setTimeout(() => {
                    agent.currentTasks.shift();
                    if (currentActiveCard === agentKey) { /* Re-render card */ }
                    if (agentElement) agentElement.style.width = '0%';
                }, 1500);
            }
            agent.currentTasks[0].progress = Math.min(progress, 100);
            if (agentElement) agentElement.style.width = `${agent.currentTasks[0].progress}%`;
            
            // Update card progress bar if open
            if (currentActiveCard === agentKey) {
                const cardProgressBar = document.querySelector(`#card-${agentKey} .task-queue .progress-fill`);
                const cardProgressText = document.querySelector(`#card-${agentKey} .task-queue .task-item div:nth-child(2) div:last-child`);
                if (cardProgressBar) cardProgressBar.style.width = `${agent.currentTasks[0].progress}%`;
                if (cardProgressText) cardProgressText.textContent = `${agent.currentTasks[0].progress}%`;

            }

        }, 300);
        systemMetrics.tasksQueued++;
        updateSystemStatus();
    }

    function toggleAgentStatus(agentKey) {
        const agent = llmAgents[agentKey];
        const statusCycle = ['active', 'idle', 'sleeping'];
        const currentIndex = statusCycle.indexOf(agent.status);
        const nextIndex = (currentIndex + 1) % statusCycle.length;
        const oldStatus = agent.status;
        agent.status = statusCycle[nextIndex];

        const agentVisualElement = document.querySelector(`.agent-representation.${agentKey}`); // Main visual for the agent
        if(agentVisualElement) {
            agentVisualElement.classList.remove('active', 'idle', 'sleeping');
            agentVisualElement.classList.add(agent.status);
            const statusRingVisual = agentVisualElement.querySelector('.status-ring');
            if(statusRingVisual) {
                statusRingVisual.className = `status-ring ${agent.status}`;
            }
        }
        
        if (typeof addActivityItem === 'function') {
            const statusMessages = { 'active': `🚀 ${agent.name} activated`, 'idle': `⏸️ ${agent.name} idle`, 'sleeping': `💤 ${agent.name} sleeping` };
            addActivityItem(statusMessages[agent.status]);
        }
        if (currentActiveCard === agentKey) { // Re-render card
            showAgentCard(agentKey); // This will effectively refresh the card content
        }
        updateSystemStatus();
        console.log(`🔄 ${agent.name} status changed from ${oldStatus} to: ${agent.status}`);
    }

    function viewAgentLogs(agentKey) {
        const agent = llmAgents[agentKey];
        const logs = [
            `[${new Date().toLocaleTimeString()}] ${agent.name} initialized`,
            `[${new Date(Date.now() - 60000).toLocaleTimeString()}] Task completed: ${agent.currentTasks[0]?.task || 'System check'}`,
            `[${new Date(Date.now() - 120000).toLocaleTimeString()}] Schema file accessed: ${agent.activeFiles[0]}`,
            `[${new Date(Date.now() - 180000).toLocaleTimeString()}] Status change: ${agent.status}`,
            `[${new Date(Date.now() - 240000).toLocaleTimeString()}] Connection established with AZR`
        ];
        alert(`${agent.name} Activity Logs:\n\n${logs.join('\n')}`);
    }

    function highlightSchemaFile(fileName) {
        const fileElements = document.querySelectorAll('.schema-file-representation'); // Assuming schema files have this class
        fileElements.forEach(el => el.classList.remove('active'));
        
        fileElements.forEach(el => {
            if (el.textContent.includes(fileName)) {
                el.classList.add('active');
                const fileInfo = schemaFiles[fileName];
                if (fileInfo) {
                    console.log(`📁 File: ${fileName}, Purpose: ${fileInfo.purpose}`);
                    showFileInfoPopup(fileName, fileInfo); // Display popup
                    fileInfo.connectedLLMs.forEach(llmKey => {
                        const llmElement = document.querySelector(`.agent-representation.${llmKey}`);
                        if (llmElement) {
                            llmElement.style.boxShadow = `0 0 30px ${llmAgents[llmKey].color}`;
                            setTimeout(() => { llmElement.style.boxShadow = ''; }, 1500);
                        }
                    });
                }
            }
        });
    }

    function showFileInfoPopup(fileName, fileInfo) {
        let popup = document.getElementById('fileInfoPopup');
        if (popup) popup.remove(); // Remove existing popup

        popup = document.createElement('div');
        popup.id = 'fileInfoPopup'; // Added ID for easier removal
        popup.style.cssText = `
            position: fixed; top: 50%; left: 50%; transform: translate(-50%, -50%);
            background: rgba(0, 10, 30, 0.95); color: white; padding: 20px;
            border-radius: 10px; border: 2px solid #00d4ff; z-index: 2000;
            max-width: 400px; backdrop-filter: blur(5px);
            box-shadow: 0 0 30px rgba(0, 212, 255, 0.3);
        `;
        
        const isJson = fileName.endsWith('.json');
        const fileColor = isJson ? '#FFA500' : '#4CAF50';
        
        popup.innerHTML = `
            <h3 style="color: #00d4ff; margin-bottom: 15px;">
                <span style="display: inline-block; width: 12px; height: 12px; background: ${fileColor}; margin-right: 8px; border-radius: 2px;"></span>
                ${fileName}
            </h3>
            <div style="margin-bottom: 10px;"><strong>Purpose:</strong> ${fileInfo.purpose}</div>
            <div style="margin-bottom: 10px;"><strong>Status:</strong> <span style="color: ${fileInfo.status === 'active' ? '#4CAF50' : fileInfo.status === 'updating' ? '#FFA500' : '#F44336'};">${fileInfo.status.charAt(0).toUpperCase() + fileInfo.status.slice(1)}</span></div>
            <div style="margin-bottom: 10px;"><strong>Size:</strong> ${fileInfo.size}</div>
            <div style="margin-bottom: 15px;"><strong>Last Modified:</strong> ${fileInfo.lastModified}</div>
            <div style="margin-bottom: 15px;"><strong>Connected LLMs:</strong>
                <div style="display: flex; flex-wrap: wrap; gap: 5px; margin-top: 5px;">
                    ${fileInfo.connectedLLMs.map(llm => {
                        const agent = llmAgents[llm];
                        return `<span style="background: ${agent.color}; color: white; padding: 3px 8px; border-radius: 12px; font-size: 10px;">${agent.name}</span>`;
                    }).join('')}
                </div>
            </div>
            <button onclick="this.parentElement.remove()" style="background: #00d4ff; color: black; border: none; padding: 8px 15px; border-radius: 5px; cursor: pointer; font-weight: bold; display: block; margin: 0 auto;">Close</button>
        `;
        document.body.appendChild(popup);
        setTimeout(() => { if (popup.parentElement) popup.remove(); }, 8000);
    }

    function updateSystemStatus() {
        const activeLLMs = Object.values(llmAgents).filter(agent => agent.status === 'active').length;
        systemMetrics.activeLLMs = activeLLMs;
        const activeSchemas = Object.values(schemaFiles).filter(file => file.status === 'active').length;
        systemMetrics.activeSchemas = activeSchemas;

        const llmTrend = activeLLMs > systemMetrics.previousActiveLLMs ? '+1' : activeLLMs < systemMetrics.previousActiveLLMs ? '-1' : '±0';
        const schemaTrend = activeSchemas > systemMetrics.previousActiveSchemas ? '+1' : activeSchemas < systemMetrics.previousActiveSchemas ? '-1' : '±0';
        const taskTrend = systemMetrics.tasksQueued > systemMetrics.previousTasks ? `+${systemMetrics.tasksQueued - systemMetrics.previousTasks}` : systemMetrics.tasksQueued < systemMetrics.previousTasks ? `${systemMetrics.tasksQueued - systemMetrics.previousTasks}` : '±0';
        const rateTrend = parseFloat(systemMetrics.processRate) > parseFloat(systemMetrics.previousRate) ? `+${(systemMetrics.processRate - systemMetrics.previousRate).toFixed(1)}` : parseFloat(systemMetrics.processRate) < parseFloat(systemMetrics.previousRate) ? `${(systemMetrics.processRate - systemMetrics.previousRate).toFixed(1)}` : '±0';
        
        const statusPanel = document.querySelector('.unified-status-panel');
        if (statusPanel) {
            statusPanel.innerHTML = `
                <h4 style="color: #00d4ff; margin-bottom: 12px;">🔮 System Status</h4>
                <div class="status-item"><span>Active LLMs:</span><div class="status-value"><span style="color: ${activeLLMs > 3 ? '#4CAF50' : '#FFA500'};">${systemMetrics.activeLLMs}/${systemMetrics.totalLLMs}</span><span class="status-trend" style="background: ${llmTrend.includes('+') ? 'rgba(76,175,80,0.2)' : llmTrend.includes('-') ? 'rgba(244,67,54,0.2)' : 'rgba(255,255,255,0.1)'}; color: ${llmTrend.includes('+') ? '#4CAF50' : llmTrend.includes('-') ? '#F44336' : '#FFA500'};">${llmTrend}</span></div></div>
                <div class="status-item"><span>Schema Files:</span><div class="status-value"><span style="color: ${activeSchemas > 4 ? '#4CAF50' : '#FFA500'};">${systemMetrics.activeSchemas}/${systemMetrics.totalSchemas}</span><span class="status-trend" style="background: ${schemaTrend.includes('+') ? 'rgba(76,175,80,0.2)' : schemaTrend.includes('-') ? 'rgba(244,67,54,0.2)' : 'rgba(255,152,0,0.2)'}; color: ${schemaTrend.includes('+') ? '#4CAF50' : schemaTrend.includes('-') ? '#F44336' : '#FF9800'};">${schemaTrend}</span></div></div>
                <div class="status-item"><span>Agent Modules:</span><div class="status-value"><span style="color: #4CAF50;">7/7</span><span class="status-trend">✓</span></div></div>
                <div class="status-item"><span>Tasks Queued:</span><div class="status-value"><span id="taskCount">${systemMetrics.tasksQueued}</span><span class="status-trend" style="background: ${taskTrend.includes('+') ? 'rgba(255,152,0,0.2)' : taskTrend.includes('-') ? 'rgba(76,175,80,0.2)' : 'rgba(255,255,255,0.1)'}; color: ${taskTrend.includes('+') ? '#FF9800' : taskTrend.includes('-') ? '#4CAF50' : '#FFA500'};">${taskTrend}</span></div></div>
                <div class="status-item"><span>Processing Rate:</span><div class="status-value"><span id="processRate">${systemMetrics.processRate}/s</span><span class="status-trend" style="background: ${rateTrend.includes('+') ? 'rgba(76,175,80,0.2)' : rateTrend.includes('-') ? 'rgba(244,67,54,0.2)' : 'rgba(255,255,255,0.1)'}; color: ${rateTrend.includes('+') ? '#4CAF50' : rateTrend.includes('-') ? '#F44336' : '#FFA500'};">${rateTrend}</span></div></div>
            `;
        }
        systemMetrics.previousActiveLLMs = activeLLMs;
        systemMetrics.previousActiveSchemas = activeSchemas;
        systemMetrics.previousTasks = systemMetrics.tasksQueued;
        systemMetrics.previousRate = parseFloat(systemMetrics.processRate);
    }

    function startSystemMonitoring() {
        setInterval(() => {
            systemMetrics.tasksQueued = Math.max(0, systemMetrics.tasksQueued + Math.floor(Math.random() * 3) - 1);
            systemMetrics.processRate = (Math.random() * 2 + 1.5).toFixed(1);
            updateSystemStatus();
        }, 3000);
        
        setInterval(() => {
            const fileNames = Object.keys(schemaFiles);
            const randomFileKey = fileNames[Math.floor(Math.random() * fileNames.length)];
            const statuses = ['active', 'updating', 'error', 'loaded'];
            schemaFiles[randomFileKey].status = statuses[Math.floor(Math.random() * statuses.length)];
            
            const schemaElements = document.querySelectorAll('.schema-file-representation'); // Class for schema file visuals
            schemaElements.forEach(el => {
                if (el.dataset.fileName === randomFileKey) { // Assuming data-file-name attribute
                    const statusDot = el.querySelector('.file-status-dot'); // Class for status indicator
                    if (statusDot) {
                         const colors = { 'active': '#4CAF50', 'updating': '#FFA500', 'error': '#F44336', 'loaded': '#2196F3' };
                         statusDot.style.background = colors[schemaFiles[randomFileKey].status];
                    }
                }
            });
        }, 5000);
    }

    function handleKeyboardShortcuts(event) {
        if (event.key === 'Escape' && currentActiveCard) {
            hideAgentCard(currentActiveCard);
        }
        const agentKeys = ['azr', 'phi3', 'gemini', 'echo', 'qwen', 'llama'];
        const keyNum = parseInt(event.key);
        if (!isNaN(keyNum) && keyNum >= 1 && keyNum <= agentKeys.length) {
            showAgentCard(agentKeys[keyNum - 1]);
        }
    }

    const agentModules = {
        'cognitive-core': { name: 'Agent Lee Cognitive Core', file: 'pa0s6amxdb.html', description: 'Dynamic task management and priority system' },
        'llm-circular': { name: 'Agent Lee LLM Circular System', file: 'index.html', description: 'Main LLM coordination and circular processing system' },
        'agent-center': { name: 'Agent Lee Agent Center', file: 'n58i2843wi.html', description: 'Central agent management and coordination hub' },
        'database': { name: 'Agent Lee Database', file: 'uiiny02oor.html', description: 'CRM Database Dashboard and data management' },
        'todo': { name: 'Agent Lee Dynamic To-Do List', file: 'x201ms96it.html', description: 'Neuro-Operational Matrix - Live Network Dashboard' },
        'workers': { name: 'Agent Lee Workers Center', file: 'ldhc5glhjp.html', description: 'Integrated workers center and task distribution' },
        'index-card': { name: 'Agent Lee Main Card', file: 'index.html', description: 'Primary interface and system overview' }
    };

    function openModule(moduleKey) {
        const module = agentModules[moduleKey];
        if (module) {
            console.log(`🔗 Opening ${module.name}...`);
            const moduleElement = document.querySelector(`.module-button.${moduleKey.replace('-', '-')}`); // Assuming .module-button.cognitive-core etc.
            if (moduleElement) {
                moduleElement.style.transform = 'scale(1.1)';
                moduleElement.style.boxShadow = '0 0 30px rgba(0, 212, 255, 0.8)';
                setTimeout(() => { moduleElement.style.transform = ''; moduleElement.style.boxShadow = ''; }, 1000);
            }
            
            let popup = document.getElementById('moduleInfoPopup');
            if (popup) popup.remove();

            popup = document.createElement('div');
            popup.id = 'moduleInfoPopup';
            popup.style.cssText = `
                position: fixed; top: 50%; left: 50%; transform: translate(-50%, -50%);
                background: rgba(0, 10, 30, 0.95); color: white; padding: 25px;
                border-radius: 15px; border: 2px solid #00d4ff; z-index: 2000;
                max-width: 550px; backdrop-filter: blur(10px);
                box-shadow: 0 0 50px rgba(0, 212, 255, 0.5);
            `;

            const connectedLLMs = [];
            const llmKeys = Object.keys(llmAgents).filter(k => k !== 'azr');
            const numConnections = 2 + Math.floor(Math.random() * 3);
            for (let i = 0; i < numConnections; i++) {
                const randomLLM = llmKeys[Math.floor(Math.random() * llmKeys.length)];
                if (!connectedLLMs.includes(randomLLM)) connectedLLMs.push(randomLLM);
            }

            const connectedSchemas = [];
            const schemaKeys = Object.keys(schemaFiles);
            const numSchemas = 1 + Math.floor(Math.random() * 3);
            for (let i = 0; i < numSchemas; i++) {
                const randomSchema = schemaKeys[Math.floor(Math.random() * schemaKeys.length)];
                if (!connectedSchemas.includes(randomSchema)) connectedSchemas.push(randomSchema);
            }

            popup.innerHTML = `
                <h3 style="color: #00d4ff; margin-bottom: 15px; font-size: 22px;">${module.name}</h3>
                <div style="background: rgba(0,212,255,0.1); padding: 15px; border-radius: 10px; margin-bottom: 20px; border-left: 4px solid #00d4ff;"><p style="line-height: 1.5;">${module.description}</p></div>
                <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 15px; margin-bottom: 20px;">
                    <div><h4 style="color:#00d4ff; margin-bottom:10px; font-size:16px;"><svg width="16" height="16" viewBox="0 0 24 24" style="vertical-align:middle; margin-right:5px;"><path fill="#00d4ff" d="M12 1L3 5v6c0 5.55 3.84 10.74 9 12 5.16-1.26 9-6.45 9-12V5l-9-4zm0 10.99h7c-.53 4.12-3.28 7.79-7 8.94V12H5V6.3l7-3.11v8.8z"/></svg>Connected LLMs</h4><div style="display:flex; flex-wrap:wrap; gap:5px;">${connectedLLMs.map(llm => { const agent = llmAgents[llm]; return `<div style="background:${agent.color}; color:white; padding:5px 10px; border-radius:15px; font-size:12px; display:flex; align-items:center; gap:5px;"><span style="font-size:14px;">${agent.avatar}</span> ${agent.name}</div>`; }).join('')}</div></div>
                    <div><h4 style="color:#00d4ff; margin-bottom:10px; font-size:16px;"><svg width="16" height="16" viewBox="0 0 24 24" style="vertical-align:middle; margin-right:5px;"><path fill="#00d4ff" d="M14 2H6c-1.1 0-1.99.9-1.99 2L4 20c0 1.1.89 2 1.99 2H18c1.1 0 2-.9 2-2V8l-6-6zm2 16H8v-2h8v2zm0-4H8v-2h8v2zm-3-5V3.5L18.5 9H13z"/></svg>Schema Integration</h4><div style="display:flex; flex-direction:column; gap:5px;">${connectedSchemas.map(schema => { const isJson = schema.endsWith('.json'); return `<div style="background:rgba(255,255,255,0.05); padding:5px 10px; border-radius:5px; font-size:12px; border-left:3px solid ${isJson ? '#FFA500' : '#4CAF50'};">${schema}</div>`; }).join('')}</div></div>
                </div>
                <div style="background:rgba(255,255,255,0.05); padding:15px; border-radius:10px; margin-bottom:20px;"><h4 style="color:#00d4ff; margin-bottom:10px; font-size:16px;"><svg width="16" height="16" viewBox="0 0 24 24" style="vertical-align:middle; margin-right:5px;"><path fill="#00d4ff" d="M19 3h-4.18C14.4 1.84 13.3 1 12 1c-1.3 0-2.4.84-2.82 2H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zm-7 0c.55 0 1 .45 1 1s-.45 1-1 1-1-.45-1-1 .45-1 1-1zm2 14H7v-2h7v2zm3-4H7v-2h10v2zm0-4H7V7h10v2z"/></svg>Implementation Details</h4><div style="display:flex; justify-content:space-between; font-size:13px;"><div><strong>File:</strong> <span style="color:#4CAF50;">${module.file}</span></div><div><strong>Status:</strong> <span style="color:#4CAF50;">Active</span></div><div><strong>Last Updated:</strong> <span>${new Date().toLocaleString()}</span></div></div></div>
                <div style="display:flex; gap:10px; justify-content:center;">
                    <button onclick="window.open('${module.file}', '_blank')" style="background:linear-gradient(135deg, #00d4ff, #0097c2); color:white; border:none; padding:10px 20px; border-radius:8px; cursor:pointer; font-weight:bold; box-shadow:0 4px 15px rgba(0,212,255,0.3);"><svg width="16" height="16" viewBox="0 0 24 24" style="vertical-align:middle; margin-right:5px;"><path fill="currentColor" d="M19 19H5V5h7V3H5c-1.11 0-2 .9-2 2v14c0 1.1.89 2 2 2h14c1.1 0 2-.9 2-2v-7h-2v7zM14 3v2h3.59l-9.83 9.83 1.41 1.41L19 6.41V10h2V3h-7z"/></svg>Open Module</button>
                    <button onclick="this.parentElement.parentElement.remove()" style="background:rgba(255,255,255,0.1); color:white; border:1px solid rgba(255,255,255,0.3); padding:10px 20px; border-radius:8px; cursor:pointer;">Close</button>
                </div>
            `;
            document.body.appendChild(popup);
            
            connectedLLMs.forEach(llmKey => {
                const llmElement = document.querySelector(`.agent-representation.${llmKey}`);
                if (llmElement) {
                    llmElement.style.boxShadow = `0 0 30px ${llmAgents[llmKey].color}`;
                    setTimeout(() => { llmElement.style.boxShadow = ''; }, 2000);
                }
            });
            setTimeout(() => { if (popup.parentElement) popup.remove(); }, 12000);
            animateModuleConnection(moduleKey);
        }
    }

    function animateModuleConnection(moduleKey) {
        console.log(`🔗 Module ${moduleKey} activated (flow line animation placeholder)`);
    }

    // Expose functions to a global namespace for cognitive-matrix
    window.AgentLeeCognitiveMatrix = {
        showAgentCard,
        hideAgentCard,
        runAgentTask,
        toggleAgentStatus,
        viewAgentLogs,
        highlightSchemaFile,
        openModule,
        // Expose data for debugging if needed, careful with direct modification
        llmAgents, 
        schemaFiles,
        systemMetrics,
        agentModules
    };
})();
// --- END OF FILE cognitive-matrix.js ---

// --- START OF FILE console_test_script.js ---
// Console Test Script for Agent Lee Intelligence
// Copy and paste this into the browser console on index.html
(function() {
    console.log('🧠 Agent Lee Intelligence Test Script');
    console.log('=====================================');

    // Test 1: Check if core components are available
    function testCoreComponents() {
        console.log('\n🔍 Test 1: Checking Core Components');
        console.log('EchoDispatcher:', typeof window.EchoDispatcher !== 'undefined' ? '✅ Available' : '❌ Missing');
        console.log('AZRBridgeClient:', typeof window.AZRBridgeClient !== 'undefined' ? '✅ Available' : '❌ Missing');
        console.log('RealLLMManager (formerly FrontendLLMManager):', typeof window.RealLLMManager !== 'undefined' ? '✅ Available' : '❌ Missing');
    }
    testCoreComponents(); // Run immediately

    // Test 2: Test AZR Bridge Connection
    async function testAZRBridge() {
        console.log('\n🔗 Test 2: Testing AZR Bridge Connection');
        try {
            if (!window.AZRBridgeClient) {
                console.log('❌ AZRBridgeClient not available');
                return false;
            }
            
            console.log('🔄 Connecting to AZR Bridge...');
            if (!window.AZRBridgeClient.isConnected) {
                await window.AZRBridgeClient.connect();
            }
            
            console.log('📤 Sending test task...');
            const response = await window.AZRBridgeClient.sendTask({
                type: 'azr_task',
                prompt: 'What is 2 + 2? Explain briefly.',
                request_id: `console_test_${Date.now()}`
            });
            
            console.log('✅ AZR Bridge Response:', response);
            return true;
        } catch (error) {
            console.log('❌ AZR Bridge Test Failed:', error);
            return false;
        }
    }

    // Test 3: Test LLM Routing
    async function testLLMRouting() {
        console.log('\n🎯 Test 3: Testing LLM Routing');
        try {
            if (!window.EchoDispatcher) { // Assuming EchoDispatcher is the one to test
                console.log('❌ EchoDispatcher not available');
                return false;
            }
            
            // Test routing decisions
            const testCases = [
                { input: 'Analyze the economic impact of artificial intelligence', expected: 'azr' },
                { input: 'Validate email format: <EMAIL>', expected: 'phi3' },
                { input: 'Generate a creative story about robots', expected: 'gemini' },
                { input: 'Summarize this text for me', expected: 'webllm' } // or azr if webllm not loaded
            ];
            
            console.log('🧠 Testing routing decisions:');
            for (const test of testCases) {
                const routing = window.EchoDispatcher.routeTask(test.input);
                const match = routing.target.startsWith(test.expected) ? '✅' : '⚠️'; // Use startsWith for fallbacks
                console.log(`${match} "${test.input.substring(0, 30)}..." → ${routing.target} (confidence: ${routing.confidence.toFixed(2)})`);
            }
            
            return true;
        } catch (error) {
            console.log('❌ LLM Routing Test Failed:', error);
            return false;
        }
    }

    // Test 4: Test Full Intelligence Pipeline
    async function testFullIntelligence() {
        console.log('\n🧠 Test 4: Testing Full Intelligence Pipeline');
        try {
            if (!window.EchoDispatcher) {
                console.log('❌ EchoDispatcher not available');
                return false;
            }
            
            console.log('🔄 Executing intelligent task...');
            const result = await window.EchoDispatcher.executeTask('What are the main benefits of renewable energy?', {
                source: 'console_test',
                timestamp: new Date().toISOString()
            });
            
            console.log('✅ Intelligence Test Result:');
            console.log('- Success:', result.success);
            console.log('- Source:', result.source);
            console.log('- Response:', result.result);
            
            return result.success;
        } catch (error) {
            console.log('❌ Full Intelligence Test Failed:', error);
            return false;
        }
    }

    // Test 5: Test System Status
    function testSystemStatus() {
        console.log('\n📊 Test 5: System Status Check');
        try {
            if (window.EchoDispatcher && typeof window.EchoDispatcher.getSystemStatus === 'function') {
                const status = window.EchoDispatcher.getSystemStatus();
                console.log('📢 Echo Dispatcher Status:', status);
                console.log('✅ EchoDispatcher status function available');
            } else {
                 console.log('❌ EchoDispatcher status function not available or EchoDispatcher itself is missing.');
            }
            
            if (window.AZRBridgeClient) {
                const azrStatus = window.AZRBridgeClient.getConnectionStatus();
                console.log('🔗 AZR Bridge Status:', azrStatus);
            }
            
            if (window.RealLLMManager) { // Changed from FrontendLLMManager
                const llmStatus = window.RealLLMManager.getModelStatus();
                console.log('🧠 LLM Status:', llmStatus);
            }
            
            return true;
        } catch (error) {
            console.log('❌ System Status Test Failed:', error);
            return false;
        }
    }

    // Run all tests
    async function runAllTests() {
        console.log('\n🚀 Running All Intelligence Tests');
        console.log('==================================');
        
        testCoreComponents(); // Ensure this is run before others if they depend on its output
        
        const results = {
            azrBridge: await testAZRBridge(),
            llmRouting: await testLLMRouting(),
            fullIntelligence: await testFullIntelligence(),
            systemStatus: testSystemStatus()
        };
        
        console.log('\n📋 Test Results Summary:');
        console.log('========================');
        console.log('AZR Bridge:', results.azrBridge ? '✅ PASS' : '❌ FAIL');
        console.log('LLM Routing:', results.llmRouting ? '✅ PASS' : '❌ FAIL');
        console.log('Full Intelligence:', results.fullIntelligence ? '✅ PASS' : '❌ FAIL');
        console.log('System Status:', results.systemStatus ? '✅ PASS' : '❌ FAIL');
        
        const allPassed = Object.values(results).every(result => result);
        console.log('\n🎯 Overall Result:', allPassed ? '✅ AGENT LEE IS INTELLIGENT!' : '❌ Issues detected');
        
        return results;
    }

    // Quick test functions
    window.quickTestAZR = testAZRBridge;
    window.quickTestRouting = testLLMRouting;
    window.quickTestIntelligence = testFullIntelligence;
    window.runAllIntelligenceTests = runAllTests;

    console.log('\n🎮 Available Test Functions:');
    console.log('- quickTestAZR() - Test AZR Bridge connection');
    console.log('- quickTestRouting() - Test LLM routing logic');
    console.log('- quickTestIntelligence() - Test full intelligence pipeline');
    console.log('- runAllIntelligenceTests() - Run complete test suite');
    console.log('\nExample: await runAllIntelligenceTests()');
})();
// --- END OF FILE console_test_script.js ---

// --- START OF FILE main.js (Electron Main Process) ---
// THIS CODE IS INTENDED FOR THE ELECTRON MAIN PROCESS.
// IT WILL NOT RUN CORRECTLY IN A BROWSER ENVIRONMENT.
AgentLee.ElectronMainProcessCode = `
const { app, BrowserWindow, ipcMain, dialog, shell } = require('electron');
const path = require('path');
const { spawn } = require('child_process');
const fs = require('fs');
const WebSocket = require('ws');

// Keep a global reference of the window object
let mainWindow;
let azrBridgeProcess = null;
let isDev = process.env.ELECTRON_IS_DEV === 'true';

// Paths for bundled resources
const resourcesPath = isDev ? process.cwd() : process.resourcesPath;
const modelsPath = path.join(resourcesPath, 'models');
const pythonPath = path.join(resourcesPath, 'python_dist'); // Ensure this matches your build structure

function createWindow() {
  // Create the browser window
  mainWindow = new BrowserWindow({
    width: 1400,
    height: 900,
    minWidth: 1200,
    minHeight: 800,
    webPreferences: {
      nodeIntegration: false,
      contextIsolation: true,
      enableRemoteModule: false,
      preload: path.join(__dirname, 'preload.js'), // Assumes preload.js is in the same dir as main.js
      webSecurity: !isDev 
    },
    // icon: path.join(__dirname, '../build/icon.png'), // Adjust path as needed
    show: false,
    titleBarStyle: 'default'
  });

  // Load the app
  const startUrl = isDev 
    ? 'file://' + path.join(process.cwd(), 'index.html') // Dev: load index.html from project root
    : 'file://' + path.join(__dirname, '../index.html'); // Prod: load index.html from build output (e.g., dist/ or app.asar.unpacked/)
  
  mainWindow.loadURL(startUrl);

  // Show window when ready to prevent visual flash
  mainWindow.once('ready-to-show', () => {
    mainWindow.show();
    startAZRBridge();
  });

  if (isDev) {
    mainWindow.webContents.openDevTools();
  }

  mainWindow.on('closed', () => {
    mainWindow = null;
    stopAZRBridge();
  });

  mainWindow.webContents.setWindowOpenHandler(({ url }) => {
    shell.openExternal(url);
    return { action: 'deny' };
  });
}

function findPythonExecutable() {
  const possibleScripts = ['azr_bridge.py', 'azr_bridge_main.py']; // Common names for the main script
  const devScriptPath = possibleScripts.map(s => path.join(process.cwd(), 'python_scripts', s)).find(p => fs.existsSync(p)); // Dev: look in a subfolder like 'python_scripts'

  const prodExecutableName = process.platform === 'win32' ? 'azr_bridge.exe' : 'azr_bridge';
  const prodExecutablePath = path.join(pythonPath, prodExecutableName); // Prod: look in bundled python_dist

  if (isDev && devScriptPath) {
    console.log("Dev mode: Using Python script at", devScriptPath);
    return { type: 'script', path: devScriptPath };
  } else if (fs.existsSync(prodExecutablePath)) {
    console.log("Prod mode: Using bundled executable at", prodExecutablePath);
    return { type: 'exe', path: prodExecutablePath };
  } else if (devScriptPath) { // Fallback to dev script path if prod not found, even in non-dev mode (e.g. testing packaged app)
      console.warn("Prod executable not found, falling back to dev script path:", devScriptPath);
      return { type: 'script', path: devScriptPath };
  }

  console.warn('⚠️ Python executable/script for AZR bridge not found.');
  return null;
}

function startAZRBridge() {
  const pythonInfo = findPythonExecutable();
  
  if (!pythonInfo) {
    mainWindow.webContents.send('azr-bridge-status', { 
      status: 'unavailable', 
      message: 'Python backend for AZR bridge not found' 
    });
    return;
  }

  console.log('🚀 Starting AZR bridge with:', pythonInfo.path);

  try {
    if (pythonInfo.type === 'exe') {
      azrBridgeProcess = spawn(pythonInfo.path, [], {
        cwd: path.dirname(pythonInfo.path),
        stdio: ['pipe', 'pipe', 'pipe', 'ipc'] // Add 'ipc' for potential direct communication
      });
    } else { // script
      // Try common Python executable names
      const pythonCmds = process.platform === 'win32' ? ['python.exe', 'python3.exe', 'py.exe'] : ['python3', 'python'];
      let foundPythonCmd = null;
      // You might need a more robust way to find python, e.g., checking PATH or using a known virtual env
      // For simplicity, this example assumes 'python' or 'python3' is in PATH
      // A better approach for packaged apps is to bundle Python itself.
      foundPythonCmd = pythonCmds.find(cmd => {
          try { require('child_process').execSync(\`\${cmd} --version\`); return true; } 
          catch (e) { return false; }
      }) || 'python'; // Default to 'python' if none found by version check

      azrBridgeProcess = spawn(foundPythonCmd, [pythonInfo.path], {
        cwd: path.dirname(pythonInfo.path),
        stdio: ['pipe', 'pipe', 'pipe', 'ipc']
      });
    }

    azrBridgeProcess.stdout.on('data', (data) => {
      const logMsg = data.toString().trim();
      console.log('AZR Bridge:', logMsg);
      if (mainWindow && mainWindow.webContents) {
        mainWindow.webContents.send('azr-bridge-log', logMsg);
      }
    });

    azrBridgeProcess.stderr.on('data', (data) => {
      const errorMsg = data.toString().trim();
      console.error('AZR Bridge Error:', errorMsg);
      if (mainWindow && mainWindow.webContents) {
        mainWindow.webContents.send('azr-bridge-error', errorMsg);
      }
    });

    azrBridgeProcess.on('close', (code) => {
      console.log(\`AZR Bridge process exited with code \${code}\`);
      if (mainWindow && mainWindow.webContents) {
        mainWindow.webContents.send('azr-bridge-status', { status: 'stopped', code: code });
      }
      azrBridgeProcess = null;
    });

    azrBridgeProcess.on('error', (error) => {
      console.error('Failed to start AZR Bridge:', error);
      if (mainWindow && mainWindow.webContents) {
        mainWindow.webContents.send('azr-bridge-status', { status: 'error', error: error.message });
      }
    });

    setTimeout(testAZRConnection, 3000);

  } catch (error) {
    console.error('Error starting AZR bridge:', error);
    if (mainWindow && mainWindow.webContents) {
      mainWindow.webContents.send('azr-bridge-status', { status: 'error', error: error.message });
    }
  }
}

function testAZRConnection() {
  if (!mainWindow || !mainWindow.webContents) return; // Window might be closed

  const ws = new WebSocket('ws://localhost:8765'); // Default port for the Python WebSocket server
  
  ws.on('open', () => {
    console.log('✅ AZR Bridge WebSocket connection successful');
    mainWindow.webContents.send('azr-bridge-status', { status: 'connected' });
    ws.close();
  });

  ws.on('error', (error) => {
    console.warn('⚠️ AZR Bridge WebSocket connection failed:', error.message);
    mainWindow.webContents.send('azr-bridge-status', { 
      status: 'connection-failed',
      error: error.message 
    });
  });
}

function stopAZRBridge() {
  if (azrBridgeProcess) {
    console.log('🛑 Stopping AZR bridge');
    azrBridgeProcess.kill();
    azrBridgeProcess = null;
  }
}

app.whenReady().then(createWindow);

app.on('window-all-closed', () => {
  stopAZRBridge();
  if (process.platform !== 'darwin') {
    app.quit();
  }
});

app.on('activate', () => {
  if (BrowserWindow.getAllWindows().length === 0) {
    createWindow();
  }
});

app.on('before-quit', () => {
  stopAZRBridge();
});

ipcMain.handle('get-app-info', () => {
  return {
    version: app.getVersion(),
    name: app.getName(),
    isDev: isDev,
    resourcesPath: resourcesPath,
    modelsPath: modelsPath
  };
});

ipcMain.handle('restart-azr-bridge', () => {
  stopAZRBridge();
  setTimeout(startAZRBridge, 1000);
  return { success: true };
});

ipcMain.handle('get-azr-bridge-status', () => {
  return {
    running: azrBridgeProcess !== null,
    pid: azrBridgeProcess ? azrBridgeProcess.pid : null
  };
});

ipcMain.handle('show-message-box', async (event, options) => {
  const result = await dialog.showMessageBox(mainWindow, options);
  return result;
});
ipcMain.handle('show-open-dialog', async (event, options) => {
  const result = await dialog.showOpenDialog(mainWindow, options);
  return result;
});
ipcMain.handle('show-save-dialog', async (event, options) => {
  const result = await dialog.showSaveDialog(mainWindow, options);
  return result;
});

app.setAsDefaultProtocolClient('agent-lee');

console.log('🧠 Agent Lee Cognitive System - Electron Main Process Started');
console.log('📁 Resources Path:', resourcesPath);
console.log('🤖 Models Path:', modelsPath);
console.log('🐍 Python Dist Path:', pythonPath);
`;
// --- END OF FILE main.js (Electron Main Process) ---

// --- START OF FILE preload.js (Electron Preload Script) ---
// THIS CODE IS INTENDED FOR THE ELECTRON PRELOAD SCRIPT.
// IT WILL NOT RUN CORRECTLY IN A BROWSER ENVIRONMENT.
AgentLee.ElectronPreloadScriptCode = `
const { contextBridge, ipcRenderer } = require('electron');

contextBridge.exposeInMainWorld('electronAPI', {
  getAppInfo: () => ipcRenderer.invoke('get-app-info'),
  restartAZRBridge: () => ipcRenderer.invoke('restart-azr-bridge'),
  getAZRBridgeStatus: () => ipcRenderer.invoke('get-azr-bridge-status'),
  showMessageBox: (options) => ipcRenderer.invoke('show-message-box', options),
  showOpenDialog: (options) => ipcRenderer.invoke('show-open-dialog', options),
  showSaveDialog: (options) => ipcRenderer.invoke('show-save-dialog', options),
  onAZRBridgeStatus: (callback) => {
    const handler = (event, data) => callback(data);
    ipcRenderer.on('azr-bridge-status', handler);
    return () => ipcRenderer.removeListener('azr-bridge-status', handler); // Return a cleanup function
  },
  onAZRBridgeLog: (callback) => {
    const handler = (event, data) => callback(data);
    ipcRenderer.on('azr-bridge-log', handler);
    return () => ipcRenderer.removeListener('azr-bridge-log', handler);
  },
  onAZRBridgeError: (callback) => {
    const handler = (event, data) => callback(data);
    ipcRenderer.on('azr-bridge-error', handler);
    return () => ipcRenderer.removeListener('azr-bridge-error', handler);
  },
  removeAllListeners: (channel) => {
    ipcRenderer.removeAllListeners(channel);
  }
});

contextBridge.exposeInMainWorld('isElectron', true);

contextBridge.exposeInMainWorld('processInfo', {
  platform: process.platform,
  arch: process.arch,
  versions: process.versions // process.versions is already an object
});

console.log('🔗 Agent Lee Electron Preload Script Loaded');
`;
// --- END OF FILE preload.js (Electron Preload Script) ---

// --- START OF FILE sw.js (Service Worker) ---
// THIS CODE IS INTENDED FOR A SERVICE WORKER.
// IT WILL NOT RUN CORRECTLY IF PASTED DIRECTLY INTO MAIN BROWSER JS.
AgentLee.ServiceWorkerCode = `
/**
 * Agent Lee Service Worker
 * Handles caching and offline functionality
 */

const CACHE_NAME = 'agent-lee-v1';
// In a single-file scenario, these URLs would ideally point to sections within the file or be removed.
// For this exercise, keeping them, but noting the implication.
const CACHE_URLS = [
    '/', // Cache the main HTML
    // The single JS file itself if loaded via a consistent URL.
    // However, if the JS is inline or dynamically generated blob, this is tricky.
    // './supreme_agent_lee.js', // Example if it were a separate file.
    // Other assets like CSS, images would go here.
    // Given the prompt, focusing on JS files that were separate:
    '/production_monitoring.js', // These are now part of the big file
    '/automated_test_suite.js',
    '/schema_migration_system.js',
    '/cognitive-matrix.js',
    '/agentMemory.js',
    '/agentStatus.js',
    '/syncData.js',
    '/systemHealth.js',
    '/cameraDiagnostics.js'
];

self.addEventListener('install', (event) => {
    console.log('🔧 Service Worker installing...');
    event.waitUntil(
        caches.open(CACHE_NAME)
            .then((cache) => {
                console.log('📦 Caching app shell');
                const validUrls = CACHE_URLS.filter(url => 
                    url.startsWith('/') || url.startsWith('http') && // Allow relative and absolute URLs
                    !url.startsWith('chrome-extension:') && 
                    !url.startsWith('moz-extension:')
                );
                // For a truly single page app with JS in HTML, only '/' might be relevant here
                // or the main HTML file name.
                return cache.addAll(validUrls.length > 0 ? validUrls : ['/']);
            })
            .catch((error) => {
                console.error('❌ Cache installation failed:', error);
            })
    );
    self.skipWaiting();
});

self.addEventListener('activate', (event) => {
    console.log('✅ Service Worker activated');
    event.waitUntil(
        caches.keys().then((cacheNames) => {
            return Promise.all(
                cacheNames.map((cacheName) => {
                    if (cacheName !== CACHE_NAME) {
                        console.log('🗑️ Deleting old cache:', cacheName);
                        return caches.delete(cacheName);
                    }
                })
            );
        })
    );
    return self.clients.claim();
});

self.addEventListener('fetch', (event) => {
    const request = event.request;

    if (!request.url.startsWith('http')) {
        return; // Skip non-http requests
    }
    if (request.method !== 'GET') {
        return; // Skip non-GET requests
    }

    // Network first, then cache strategy for dynamic content, or cache first for app shell.
    // For simplicity, let's do cache-first for GET requests.
    event.respondWith(
        caches.match(request)
            .then((response) => {
                if (response) {
                    // console.log('📦 Serving from cache:', request.url);
                    return response;
                }
                // console.log('🌐 Fetching from network:', request.url);
                return fetch(request).then((networkResponse) => {
                    if (!networkResponse || networkResponse.status !== 200 || networkResponse.type !== 'basic') {
                        return networkResponse;
                    }
                    const responseToCache = networkResponse.clone();
                    caches.open(CACHE_NAME)
                        .then((cache) => {
                            // Only cache valid URLs and methods
                            if (request.url.startsWith('http') &&
                                !request.url.startsWith('chrome-extension:') &&
                                !request.url.startsWith('moz-extension:') &&
                                request.method === 'GET') {
                                try {
                                    cache.put(request, responseToCache);
                                } catch (cacheError) {
                                    console.warn('⚠️ Cache put failed:', cacheError.message, request.url);
                                }
                            }
                        })
                        .catch((error) => {
                            console.warn('⚠️ Failed to cache response:', error, request.url);
                        });
                    return networkResponse;
                }).catch(error => {
                     console.error('❌ Fetch failed, network error:', error, request.url);
                     // Fallback for navigation requests if offline
                     if (request.mode === 'navigate' || (request.method === 'GET' && request.headers.get('accept').includes('text/html'))) {
                         return caches.match('/') || caches.match('/index.html');
                     }
                     // For other assets, could return a generic offline response or just let the error propagate
                });
            })
    );
});

self.addEventListener('message', (event) => {
    console.log('📨 Service Worker received message:', event.data);
    if (event.data && event.data.type === 'SKIP_WAITING') {
        self.skipWaiting();
    }
    if (event.data && event.data.type === 'GET_VERSION') {
        event.ports[0].postMessage({ version: CACHE_NAME });
    }
    if (event.data && event.data.type === 'CLEAR_CACHE') {
        caches.delete(CACHE_NAME).then(() => {
            event.ports[0].postMessage({ success: true });
        });
    }
});

// Optional: Background sync and Push notifications if needed
self.addEventListener('sync', (event) => {
    console.log('🔄 Background sync triggered:', event.tag);
    if (event.tag === 'agent-lee-sync') {
        event.waitUntil(
            Promise.resolve().then(() => console.log('✅ Background sync completed'))
        );
    }
});

self.addEventListener('push', (event) => {
    console.log('📱 Push notification received:', event);
    const options = {
        body: event.data ? event.data.text() : 'Agent Lee notification',
        // icon: '/favicon.ico', // Ensure these assets are cached or available
        // badge: '/favicon.ico'
    };
    event.waitUntil(
        self.registration.showNotification('Agent Lee', options)
    );
});

self.addEventListener('notificationclick', (event) => {
    console.log('🔔 Notification clicked:', event);
    event.notification.close();
    event.waitUntil(clients.openWindow('/'));
});

console.log('🚀 Agent Lee Service Worker loaded');
`;
// --- END OF FILE sw.js (Service Worker) ---


// --- START OF Front-End Agent Lee Integration Modules ---
// (agentMemory.js, agentStatus.js, syncData.js, cameraDiagnostics.js, systemHealth.js - API client versions)

// 🧠 Module 1: agentMemory.js (API Client)
AgentLee.API.storeAgentMemory = async function(agentId, data) {
  try {
    const response = await fetch('http://localhost:3001/api/memory/store', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ agentId, data })
    });
    if (!response.ok) throw new Error(`HTTP error! status: ${response.status}`);
    return await response.json();
  } catch (error) {
    console.error('Error storing agent memory (API):', error);
    throw error;
  }
};

AgentLee.API.fetchAgentMemory = async function() {
  try {
    const response = await fetch('http://localhost:3001/api/memory/retrieve');
    if (!response.ok) throw new Error(`HTTP error! status: ${response.status}`);
    return await response.json();
  } catch (error) {
    console.error('Error fetching agent memory (API):', error);
    throw error;
  }
};

// 🧠 Module 2: agentStatus.js (API Client)
AgentLee.API.sendAgentStatus = async function(agentId, status) {
  try {
    const response = await fetch('http://localhost:3001/api/agents/status', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ agentId, status })
    });
    if (!response.ok) throw new Error(`HTTP error! status: ${response.status}`);
    return await response.json();
  } catch (error) {
    console.error('Error sending agent status (API):', error);
    throw error;
  }
};

AgentLee.API.getAgentTasks = async function(agentId) {
  try {
    const response = await fetch(`http://localhost:3001/api/agents/tasks/${agentId}`);
    if (!response.ok) throw new Error(`HTTP error! status: ${response.status}`);
    return await response.json();
  } catch (error) {
    console.error('Error fetching agent tasks (API):', error);
    throw error;
  }
};

// 🧠 Module 3: syncData.js (API Client)
AgentLee.API.uploadSyncData = async function(syncPayload) {
  try {
    const response = await fetch('http://localhost:3001/api/sync/upload', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(syncPayload)
    });
    if (!response.ok) throw new Error(`HTTP error! status: ${response.status}`);
    return await response.json();
  } catch (error) {
    console.error('Error uploading sync data (API):', error);
    throw error;
  }
};

// 🧠 Module 4: cameraDiagnostics.js (API Client)
AgentLee.API.checkCameraStatus = async function() {
  try {
    const response = await fetch('http://localhost:3001/api/camera/status');
    if (!response.ok) throw new Error(`HTTP error! status: ${response.status}`);
    return await response.json();
  } catch (error) {
    console.error('Error checking camera status (API):', error);
    throw error;
  }
};

AgentLee.API.reportCameraIssue = async function(details) {
  try {
    const response = await fetch('http://localhost:3001/api/camera/report', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(details)
    });
    if (!response.ok) throw new Error(`HTTP error! status: ${response.status}`);
    return await response.json();
  } catch (error) {
    console.error('Error reporting camera issue (API):', error);
    throw error;
  }
};

// 🧠 Module 5: systemHealth.js (API Client)
AgentLee.API.getSystemHealth = async function() {
  try {
    const response = await fetch('http://localhost:3001/api/diagnostics/health');
    if (!response.ok) throw new Error(`HTTP error! status: ${response.status}`);
    return await response.json();
  } catch (error) {
    console.error('Error fetching system health (API):', error);
    throw error;
  }
};

// Main module info for API clients
AgentLee.API.AGENT_LEE_MODULES_INFO = {
  version: '1.0.0',
  description: 'Front-end integration modules for Agent Lee backend API',
  baseUrl: 'http://localhost:3001/api',
  modules: [
    'agentMemory',
    'agentStatus',
    'syncData',
    'cameraDiagnostics',
    'systemHealth'
  ]
};
// --- END OF Front-End Agent Lee Integration Modules ---


// --- Initialization call examples (if needed, ensure DOM is ready) ---
document.addEventListener('DOMContentLoaded', () => {
    console.log("DOM fully loaded and parsed. Initializing Agent Lee components.");

    // Example: If cognitive-matrix relies on DOM elements being present for its UI
    if (window.AgentLeeCognitiveMatrix && typeof AgentLeeCognitiveMatrix.initializeSystem === 'function') {
        // AgentLeeCognitiveMatrix.initializeSystem(); // This is already called inside its own DOMContentLoaded
    }
    
    // Example: If a main app setup function is needed
    // if (typeof mainAppSetup === 'function') {
    //    mainAppSetup();
    // }

    // If using the Service Worker code
    if ('serviceWorker' in navigator && AgentLee.ServiceWorkerCode) {
      const swBlob = new Blob([AgentLee.ServiceWorkerCode], { type: 'application/javascript' });
      const swUrl = URL.createObjectURL(swBlob);
      navigator.serviceWorker.register(swUrl)
        .then(registration => console.log('Service Worker registered with scope:', registration.scope))
        .catch(error => console.error('Service Worker registration failed:', error));
    } else {
        console.warn("Service Worker not supported or AgentLee.ServiceWorkerCode not defined.");
    }
});


console.log("Agent Lee Supreme JS File loaded. All modules combined.");
})(); // End of outer scope