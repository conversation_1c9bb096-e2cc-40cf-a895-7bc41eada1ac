[{"Schema Name": "AgentCoreSchema", "Purpose": "Tracks agent roles, tools, energy, task flow"}, {"Schema Name": "WorkerBehaviorSchema", "Purpose": "Captures worker performance, interaction cycles"}, {"Schema Name": "LLMLifecycleSchema", "Purpose": "Manages LLM learning, sync, and feedback loops"}, {"Schema Name": "TaskMemorySchema", "Purpose": "Logs agent+worker actions on ToDo notepad"}, {"Schema Name": "HealingMatrixSchema", "Purpose": "Self-repair rules for data/model consistency"}, {"Schema Name": "ButtonLogicSchema", "Purpose": "Detects abnormal usage patterns on UI buttons"}, {"Schema Name": "CameraDiagnosticsSchema", "Purpose": "Monitors camera usage & user issues"}, {"Schema Name": "SpeechPauseSchema", "Purpose": "Handles user hesitation, stutter, double-actions"}, {"Schema Name": "AgentWorkerBridgeSchema", "Purpose": "Ensures agent-worker collaboration"}, {"Schema Name": "StructuredFallbackSchema", "Purpose": "Allows switching between structured/unstructured task logs"}, {"Schema Name": "PromptDirectiveSchema", "Purpose": "Stores LLM/agent fallback prompts"}, {"Schema Name": "SMSCallSchema", "Purpose": "Handles user phone/SMS with failover triggers"}, {"Schema Name": "InputConflictSchema", "Purpose": "Captures triple-clicks, typing + stutter + delete cycles"}, {"Schema Name": "MicroDatabaseSchema", "Purpose": "Supports agents/LLMs creating micro knowledge stores"}]