[{"Object Store": "agent_registry", "Purpose": "Stores metadata about all agents (name, role, capabilities, status, heartbeat, location)."}, {"Object Store": "worker_registry", "Purpose": "Stores metadata about all workers (status, availability, completed tasks, specialties)."}, {"Object Store": "tool_inventory", "Purpose": "Tracks all available tools, their current usage, ownership, and state (assigned, broken, idle)."}, {"Object Store": "task_routing_queue", "Purpose": "Queues instructions dispatched from LLMs and assigns tasks to agents and workers based on priority."}, {"Object Store": "agent_worker_sync_log", "Purpose": "Logs communication between agents and workers—who requested what, when, and what was confirmed."}, {"Object Store": "todo_list_notepad", "Purpose": "Stores raw collaborative data and messages from agents/workers; parsed and interpreted by the LLM."}, {"Object Store": "structured_memory_archive", "Purpose": "Maintains finalized, fully structured logs of tasks, relationships, and summaries curated by the LLM."}]