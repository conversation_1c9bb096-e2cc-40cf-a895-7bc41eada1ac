[{"Object Store": "agents", "Purpose": "Tracks all agents with status, region, and type."}, {"Object Store": "agent_workflows", "Purpose": "Defines tools, tasks, and sequences for agent actions."}, {"Object Store": "tasks", "Purpose": "Represents all tasks with triggers and assignment."}, {"Object Store": "workers", "Purpose": "Defines worker roles, types, and performance metrics."}, {"Object Store": "llm_sessions", "Purpose": "Tracks contextual LLM interactions per session."}, {"Object Store": "azr_nodes", "Purpose": "AZR memory nodes, linked vectors, and entropy scores."}, {"Object Store": "execution_logs", "Purpose": "Logs all command executions, timestamps, and outputs."}, {"Object Store": "healing_logs", "Purpose": "Self-repair reports with triggers and severity."}, {"Object Store": "diagnostics", "Purpose": "Evaluations of each module’s performance."}, {"Object Store": "gui_registry", "Purpose": "Tracks visual GUI regions, positions, and states."}, {"Object Store": "telemetry", "Purpose": "Operational and behavioral metrics (user and system)."}, {"Object Store": "speech_styles", "Purpose": "Voice tone, pacing, emotional markers for <PERSON>."}, {"Object Store": "engagement_signals", "Purpose": "User engagement heuristics like hesitations or bursts."}, {"Object Store": "user_behavior", "Purpose": "Schema to track attitude, tone, hesitancy of users."}, {"Object Store": "memory_fragments", "Purpose": "Stores episodic, semantic, procedural memory."}, {"Object Store": "learning_models", "Purpose": "Lifelong learning metrics and embedded models."}, {"Object Store": "local_db_refs", "Purpose": "Smaller databases created for LLMs and agents to reason independently."}, {"Object Store": "knowledge_updates", "Purpose": "Incremental updates to knowledge and schema learning."}, {"Object Store": "motivation_triggers", "Purpose": "Schema for what uplifts or frustrates users."}, {"Object Store": "emotion_tracks", "Purpose": "Emotional state mapping from text, speech, behavior."}, {"Object Store": "agent_moods", "Purpose": "Simulated moods based on context and diagnostic states."}, {"Object Store": "reflection_protocols", "Purpose": "Agent <PERSON>’s visible pauses, thoughtfulness, and summarization handling."}]