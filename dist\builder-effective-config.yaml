directories:
  output: dist
  buildResources: build
appId: com.agentlee.cognitive-system
productName: Agent <PERSON> Cognitive System
files:
  - filter:
      - electron/**/*
      - '*.html'
      - '*.js'
      - '*.css'
      - images/**/*
      - schema/**/*
      - modules/**/*
      - '!node_modules'
      - '!src'
      - '!test'
      - '!*.md'
      - '!requirements.txt'
extraResources:
  - from: llama_models
    to: models
    filter:
      - '**/*'
  - from: python_dist
    to: python
    filter:
      - '**/*'
win:
  target:
    - target: nsis
      arch:
        - x64
    - target: portable
      arch:
        - x64
  icon: build/icon.ico
mac:
  target:
    - target: dmg
      arch:
        - x64
        - arm64
  icon: build/icon.icns
  category: public.app-category.productivity
linux:
  target:
    - target: AppImage
      arch:
        - x64
    - target: deb
      arch:
        - x64
  icon: build/icon.png
  category: Office
nsis:
  oneClick: false
  allowToChangeInstallationDirectory: true
  createDesktopShortcut: true
  createStartMenuShortcut: true
electronVersion: 28.3.3
